@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* This file is for your main application CSS */

/* ProBASE Dark Theme - Global Styles */
:root {
  /* ProBASE Professional Color Scheme - Dark Grey Slate Theme */
  --primary-gradient: linear-gradient(135deg, #2f3349 0%, #3a3f5c 100%);
  --secondary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  --accent-gradient: linear-gradient(135deg, #4a4f6a 0%, #3a3f5c 100%);

  /* Background Colors - Dark Grey Slate Theme */
  --light-bg: #2f3349;           /* Dark Grey Slate - Main Background */
  --lighter-bg: #3a3f5c;         /* Lighter Grey Slate */
  --section-bg: #4a4f6a;         /* Medium Grey Slate for sections */
  --dark-bg: #252a3f;            /* Darker Grey Slate */
  --darker-bg: #1a1f2f;          /* Darkest Grey Slate */
  --content-bg: #ffffff;         /* White for content cards */

  /* Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-bg-dark: rgba(47, 51, 73, 0.95);
  --glass-border: rgba(100, 116, 139, 0.2);
  --glass-border-light: rgba(226, 232, 240, 0.8);

  /* Text Colors */
  --text-primary: #0d1421;
  --text-primary-light: #ffffff;
  --text-secondary: #64748b;
  --text-secondary-light: #94a3b8;

  /* ProBASE Brand Colors */
  --probase-primary: #1a237e;
  --probase-secondary: #f97316;
  --probase-accent: #3f51b5;
  --probase-dark: #0d1421;
  --probase-light: #f8fafc;
  --probase-gray: #64748b;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: var(--light-bg);
  color: var(--text-primary-light);
  line-height: 1.6;
}

/* Light Theme Overrides for Cards and Components */
.bg-white {
  background-color: #f9fafb !important; /* light gray for cards */
  border-color: #e5e7eb !important; /* light gray border */
}

.bg-gray-800 {
  background-color: #f9fafb !important; /* light gray for cards */
  border-color: #e5e7eb !important; /* light gray border */
}

.text-gray-900 {
  color: #1f2937 !important; /* dark text for readability */
}

.text-gray-800 {
  color: #374151 !important; /* dark gray text */
}

.text-gray-700 {
  color: #4b5563 !important; /* medium gray text */
}

.text-gray-600 {
  color: #6b7280 !important; /* gray text */
}

.text-gray-500 {
  color: #9ca3af !important; /* light gray text */
}

.text-gray-400 {
  color: #d1d5db !important; /* very light gray text */
}

.text-gray-300 {
  color: #6b7280 !important; /* readable gray text */
}

.text-white {
  color: #1f2937 !important; /* dark text instead of white */
}

.bg-gray-50 {
  background-color: #f9fafb !important; /* very light gray */
}

.bg-gray-100 {
  background-color: #f3f4f6 !important; /* light gray */
}

.bg-gray-200 {
  background-color: #e5e7eb !important; /* medium light gray */
}

.divide-gray-200 {
  border-color: #e5e7eb !important; /* light gray borders */
}

.border-gray-200 {
  border-color: #e5e7eb !important; /* light gray borders */
}

.border-gray-300 {
  border-color: #d1d5db !important; /* medium gray borders */
}

.border-gray-700 {
  border-color: #e5e7eb !important; /* light gray borders */
}

/* Light theme status colors */
.bg-green-100 {
  background-color: #dcfce7 !important; /* very light green */
}

.text-green-800 {
  color: #16a34a !important; /* medium green */
}

.text-green-600 {
  color: #22c55e !important; /* lighter green */
}

.text-green-700 {
  color: #15803d !important; /* medium green */
}

.bg-yellow-100 {
  background-color: #fef3c7 !important; /* very light yellow */
}

.text-yellow-800 {
  color: #d97706 !important; /* medium orange */
}

.bg-red-100 {
  background-color: #fee2e2 !important; /* very light red */
}

.text-red-800 {
  color: #dc2626 !important; /* medium red */
}

.text-red-600 {
  color: #ef4444 !important; /* lighter red */
}

.text-red-700 {
  color: #dc2626 !important; /* medium red */
}

.bg-blue-100 {
  background-color: #dbeafe !important; /* very light blue */
}

.text-blue-600 {
  color: #2563eb !important; /* medium blue */
}

.text-blue-800 {
  color: #1d4ed8 !important; /* darker blue */
}

/* Button overrides */
.bg-indigo-600 {
  background-color: #ea580c !important; /* orange-600 */
}

.hover\:bg-indigo-700:hover {
  background-color: #c2410c !important; /* orange-700 */
}

.text-indigo-600 {
  color: #fb923c !important; /* orange-400 */
}

.hover\:text-indigo-900:hover {
  color: #fdba74 !important; /* orange-300 */
}

/* Export Button States */
.btn-success {
  background-color: #10b981 !important; /* green-500 */
  color: white !important;
}

.btn-success:hover:not(:disabled) {
  background-color: #059669 !important; /* green-600 */
}

.btn-danger {
  background-color: #ef4444 !important; /* red-500 */
  color: white !important;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626 !important; /* red-600 */
}
