// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")
let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {_csrf_token: csrfToken}
})

// Handle file downloads
window.addEventListener("phx:download_file", (e) => {
  console.log("🔽 Download event received:", e.detail)
  const { content, filename, content_type } = e.detail

  // Handle binary content for Excel files
  let blobContent
  if (content_type.includes('spreadsheetml') || content_type.includes('excel')) {
    // Convert base64 string to binary for Excel files
    const binaryString = atob(content)
    const bytes = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    blobContent = bytes
  } else {
    // Handle text content (CSV, etc.)
    blobContent = [content]
  }

  const blob = new Blob([blobContent], { type: content_type })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  window.URL.revokeObjectURL(url)
})

// Handle simple downloads with data URLs
window.addEventListener("phx:download", (e) => {
  console.log("🔽 Simple download event received:", e.detail)
  const { url, filename } = e.detail

  const a = document.createElement('a')
  a.href = url
  a.download = filename
  a.style.display = 'none'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)

  console.log("🔽 Download triggered for:", filename)
})

// Handle toast notifications
window.addEventListener("phx:show_toast", (e) => {
  console.log("🍞 Toast notification:", e.detail)
  const { type, title, message, duration = 4000 } = e.detail

  // Create toast element
  const toast = document.createElement('div')
  toast.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white border border-gray-200 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`

  // Set color based on type
  const colorClasses = {
    success: 'border-green-500 bg-green-50',
    error: 'border-red-500 bg-red-50',
    warning: 'border-yellow-500 bg-yellow-50',
    info: 'border-blue-500 bg-blue-50'
  }

  const iconClasses = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  }

  toast.className += ` ${colorClasses[type] || colorClasses.info}`

  toast.innerHTML = `
    <div class="p-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <span class="text-lg">${iconClasses[type] || iconClasses.info}</span>
        </div>
        <div class="ml-3 w-0 flex-1">
          <p class="text-sm font-medium text-gray-900">${title}</p>
          <p class="mt-1 text-sm text-gray-500">${message}</p>
        </div>
        <div class="ml-4 flex-shrink-0 flex">
          <button class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()">
            <span class="sr-only">Close</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  `

  // Add to page
  document.body.appendChild(toast)

  // Animate in
  setTimeout(() => {
    toast.classList.remove('translate-x-full')
  }, 100)

  // Auto remove after duration
  setTimeout(() => {
    toast.classList.add('translate-x-full')
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast)
      }
    }, 300)
  }, duration)
})

// Show progress bar on live navigation and form submits
topbar.config({
  barColors: {0: "#29d"},
  shadowColor: "rgba(0, 0, 0, .3)",
  barThickness: 2,
  timeout: 10000  // Force hide after 10 seconds
})

window.addEventListener("phx:page-loading-start", _info => {
  topbar.show(300)
})

window.addEventListener("phx:page-loading-stop", _info => {
  topbar.hide()
})

// Ensure topbar is hidden when page errors occur
window.addEventListener("phx:error", _info => {
  topbar.hide()
})

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

// User Dropdown Functionality
window.toggleUserDropdown = function() {
  const dropdown = document.getElementById('user-dropdown-menu')
  const arrow = document.getElementById('dropdown-arrow')
  const trigger = dropdown.previousElementSibling

  if (dropdown.classList.contains('opacity-0')) {
    // Show dropdown
    dropdown.classList.remove('opacity-0', 'scale-95', 'pointer-events-none')
    dropdown.classList.add('opacity-100', 'scale-100')
    arrow.style.transform = 'rotate(180deg)'
    trigger.setAttribute('aria-expanded', 'true')
  } else {
    // Hide dropdown
    dropdown.classList.add('opacity-0', 'scale-95', 'pointer-events-none')
    dropdown.classList.remove('opacity-100', 'scale-100')
    arrow.style.transform = 'rotate(0deg)'
    trigger.setAttribute('aria-expanded', 'false')
  }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
  const dropdown = document.getElementById('user-dropdown')
  if (dropdown && !dropdown.contains(event.target)) {
    const menu = document.getElementById('user-dropdown-menu')
    const arrow = document.getElementById('dropdown-arrow')
    const trigger = dropdown.querySelector('button')

    if (menu && !menu.classList.contains('opacity-0')) {
      menu.classList.add('opacity-0', 'scale-95', 'pointer-events-none')
      menu.classList.remove('opacity-100', 'scale-100')
      if (arrow) arrow.style.transform = 'rotate(0deg)'
      if (trigger) trigger.setAttribute('aria-expanded', 'false')
    }
  }
})

// Close dropdown on escape key
document.addEventListener('keydown', function(event) {
  if (event.key === 'Escape') {
    const menu = document.getElementById('user-dropdown-menu')
    const arrow = document.getElementById('dropdown-arrow')
    const trigger = document.querySelector('#user-dropdown button')

    if (menu && !menu.classList.contains('opacity-0')) {
      menu.classList.add('opacity-0', 'scale-95', 'pointer-events-none')
      menu.classList.remove('opacity-100', 'scale-100')
      if (arrow) arrow.style.transform = 'rotate(0deg)'
      if (trigger) trigger.setAttribute('aria-expanded', 'false')
    }
  }
})
