#!/usr/bin/env powershell

Write-Host "Setting up environment for Excelizer compilation..." -ForegroundColor Green

# Set MAKE environment variable to use WSL make
$env:MAKE = "wsl make"
Write-Host "MAKE environment variable set to: $($env:MAKE)" -ForegroundColor Yellow

# Get dependencies first
Write-Host "Getting dependencies..." -ForegroundColor Blue
& mix deps.get

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to get dependencies" -ForegroundColor Red
    exit 1
}

# Clean and recompile Excelizer specifically
Write-Host "Cleaning Excelizer..." -ForegroundColor Blue
& mix deps.clean excelizer

Write-Host "Re-fetching Excelizer..." -ForegroundColor Blue
& mix deps.get excelizer

Write-Host "Compiling Excelizer with WSL make..." -ForegroundColor Blue
& mix deps.compile excelizer

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Excelizer compiled successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Excelizer compilation failed" -ForegroundColor Red
    exit 1
}

Write-Host "Compilation complete!" -ForegroundColor Green
