# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :reconciliation,
  ecto_repos: [Reconciliation.Repo],
  generators: [timestamp_type: :utc_datetime]

# Configures the endpoint
config :reconciliation, ReconciliationWeb.Endpoint,
  url: [host: "localhost"],
  # adapter: Bandit.PhoenixAdapter,  # Switched to Cowboy for WebSocket testing
  render_errors: [
    formats: [html: ReconciliationWeb.ErrorHTML, json: ReconciliationWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: Reconciliation.PubSub,
  live_view: [signing_salt: "DKfOB8Gt"]













# Configures the mailer - Gmail SMTP Configuration
config :reconciliation, Reconciliation.Mailer,
  adapter: Bamboo.SMTPAdapter,
  server: "smtp.gmail.com",
  hostname: "probase.com",
  port: 587,
  username: {:system, "GMAIL_USERNAME"}, # Gmail email address
  password: {:system, "GMAIL_APP_PASSWORD"}, # Gmail app password (not regular password)
  tls: :always, # Gmail requires TLS
  allowed_tls_versions: [:"tlsv1.2", :"tlsv1.3"],
  ssl: false, # Use TLS on port 587, not SSL
  auth: :always, # Gmail requires authentication
  retries: 3,
  no_mx_lookups: false




# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  reconciliation: [
    args:
      ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "3.4.3",
  reconciliation: [
    args: ~w(
      --config=tailwind.config.js
      --input=css/app.css
      --output=../priv/static/assets/app.css
    ),
    cd: Path.expand("../assets", __DIR__)
  ]









# Configures Elixir's Logger
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]





# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
