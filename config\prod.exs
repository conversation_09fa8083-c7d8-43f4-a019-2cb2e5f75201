import Config

# Note we also include the path to a cache manifest
# containing the digested version of static files. This
# manifest is generated by the `mix assets.deploy` task,
# which you should run after static files are built and
# before starting your production server.
config :reconciliation, ReconciliationWeb.Endpoint,
  cache_static_manifest: "priv/static/cache_manifest.json"



# Do not print debug messages in production
config :logger, level: :info

# Configure Bamboo mailer for production - Gmail SMTP Configuration
config :reconciliation, Reconciliation.Mailer,
  adapter: Bamboo.SMTPAdapter,
  server: "smtp.gmail.com",
  hostname: "probase.com",
  port: 587,
  username: {:system, "GMAIL_USERNAME"}, # Gmail email address
  password: {:system, "GMAIL_APP_PASSWORD"}, # Gmail app password (not regular password)
  tls: :always, # Gmail requires TLS
  allowed_tls_versions: [:"tlsv1.2", :"tlsv1.3"],
  ssl: false, # Use TLS on port 587, not SSL
  auth: :always, # Gmail requires authentication
  retries: 3,
  no_mx_lookups: false

# Runtime production configuration, including reading
# of environment variables, is done on config/runtime.exs.

