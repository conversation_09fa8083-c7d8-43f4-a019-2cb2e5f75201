defmodule Reconciliation.Organizations.Organization do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Accounts.User
  alias Reconciliation.Organizations.Team
  alias Reconciliation.ReconciliationRun

  @statuses ["active", "inactive", "suspended"]

  schema "organizations" do
    field :name, :string
    field :slug, :string
    field :description, :string
    field :settings, :map
    field :status, :string

    has_many :users, User
    has_many :teams, Team
    has_many :reconciliation_runs, ReconciliationRun

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(organization, attrs) do
    organization
    |> cast(attrs, [:name, :slug, :description, :settings, :status])
    |> validate_required([:name, :slug])
    |> validate_length(:name, min: 2, max: 100)
    |> validate_length(:slug, min: 2, max: 50)
    |> validate_format(:slug, ~r/^[a-z0-9-]+$/, message: "must contain only lowercase letters, numbers, and hyphens")
    |> validate_inclusion(:status, @statuses)
    |> unique_constraint(:slug)
    |> maybe_generate_slug()
  end

  defp maybe_generate_slug(changeset) do
    case get_field(changeset, :slug) do
      nil ->
        name = get_field(changeset, :name)
        if name do
          slug = generate_slug(name)
          put_change(changeset, :slug, slug)
        else
          changeset
        end
      _ ->
        changeset
    end
  end

  defp generate_slug(name) do
    name
    |> String.downcase()
    |> String.replace(~r/[^a-z0-9\s-]/, "")
    |> String.replace(~r/\s+/, "-")
    |> String.trim("-")
  end

  @doc """
  Returns all valid statuses
  """
  def statuses, do: @statuses

  @doc """
  Query for active organizations
  """
  def active(query \\ __MODULE__) do
    from org in query, where: org.status == "active"
  end

  @doc """
  Query for organizations by status
  """
  def by_status(query \\ __MODULE__, status) do
    from org in query, where: org.status == ^status
  end

  @doc """
  Query for organizations with user count
  """
  def with_user_count(query \\ __MODULE__) do
    from org in query,
      left_join: users in assoc(org, :users),
      group_by: org.id,
      select: %{org | user_count: count(users.id)}
  end

  @doc """
  Query for organizations with team count
  """
  def with_team_count(query \\ __MODULE__) do
    from org in query,
      left_join: teams in assoc(org, :teams),
      group_by: org.id,
      select: %{org | team_count: count(teams.id)}
  end

  @doc """
  Search organizations by name or slug
  """
  def search(query \\ __MODULE__, term) do
    search_term = "%#{term}%"
    from org in query,
      where: ilike(org.name, ^search_term) or ilike(org.slug, ^search_term)
  end

  @doc """
  Default settings for a new organization
  """
  def default_settings do
    %{
      "reconciliation" => %{
        "default_currency" => "MWK",
        "amount_tolerance" => "0.01",
        "date_tolerance_days" => 3,
        "auto_match_exact" => true,
        "auto_match_fuzzy" => false
      },
      "security" => %{
        "password_policy" => %{
          "min_length" => 8,
          "require_uppercase" => true,
          "require_lowercase" => true,
          "require_numbers" => true,
          "require_symbols" => false
        },
        "session_timeout_minutes" => 480,
        "max_failed_login_attempts" => 5,
        "account_lockout_duration_minutes" => 30
      },
      "notifications" => %{
        "email_notifications" => true,
        "reconciliation_complete" => true,
        "security_alerts" => true,
        "weekly_reports" => false
      },
      "branding" => %{
        "primary_color" => "#3B82F6",
        "logo_url" => nil,
        "custom_css" => nil
      }
    }
  end

  @doc """
  Get setting value with fallback to default
  """
  def get_setting(organization, key_path, default \\ nil) do
    settings = organization.settings || %{}
    get_in(settings, key_path) || get_in(default_settings(), key_path) || default
  end

  @doc """
  Update a specific setting
  """
  def update_setting(organization, key_path, value) do
    current_settings = organization.settings || %{}
    new_settings = put_in(current_settings, key_path, value)
    
    organization
    |> changeset(%{settings: new_settings})
  end

  @doc """
  Check if organization is active
  """
  def active?(organization) do
    organization.status == "active"
  end

  @doc """
  Get organization statistics
  """
  def statistics(organization_id) do
    from org in __MODULE__,
      where: org.id == ^organization_id,
      left_join: users in assoc(org, :users),
      left_join: teams in assoc(org, :teams),
      left_join: runs in assoc(org, :reconciliation_runs),
      group_by: org.id,
      select: %{
        organization_id: org.id,
        total_users: count(users.id, :distinct),
        total_teams: count(teams.id, :distinct),
        total_reconciliation_runs: count(runs.id, :distinct),
        created_at: org.inserted_at
      }
  end
end
