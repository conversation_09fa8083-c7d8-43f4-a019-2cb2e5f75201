defmodule Reconciliation.Organizations.Team do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Organizations.Organization
  alias Reconciliation.Accounts.{User, UserTeamMembership}

  schema "teams" do
    field :name, :string
    field :description, :string
    field :settings, :map

    belongs_to :organization, Organization
    has_many :user_team_memberships, UserTeamMembership
    has_many :users, through: [:user_team_memberships, :user]

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(team, attrs) do
    team
    |> cast(attrs, [:name, :description, :settings, :organization_id])
    |> validate_required([:name, :organization_id])
    |> validate_length(:name, min: 2, max: 100)
    |> validate_length(:description, max: 500)
    |> foreign_key_constraint(:organization_id)
    |> unique_constraint([:organization_id, :name])
  end

  @doc """
  Query teams by organization
  """
  def by_organization(query \\ __MODULE__, organization_id) do
    from team in query, where: team.organization_id == ^organization_id
  end

  @doc """
  Search teams by name or description
  """
  def search(query \\ __MODULE__, term) do
    search_term = "%#{term}%"
    from team in query,
      where: ilike(team.name, ^search_term) or ilike(team.description, ^search_term)
  end

  @doc """
  Query teams with member count (returns a map with member_count field)
  """
  def with_member_count(query \\ __MODULE__) do
    from team in query,
      left_join: memberships in assoc(team, :user_team_memberships),
      group_by: team.id,
      select: {team, count(memberships.id)}
  end

  @doc """
  Query teams with users preloaded
  """
  def with_users(query \\ __MODULE__) do
    from team in query, preload: [user_team_memberships: :user]
  end

  @doc """
  Get team statistics
  """
  def statistics(team_id) do
    from team in __MODULE__,
      where: team.id == ^team_id,
      left_join: memberships in assoc(team, :user_team_memberships),
      group_by: team.id,
      select: %{
        team_id: team.id,
        total_members: count(memberships.id, :distinct),
        team_leads: count(memberships.id, :distinct),
        created_at: team.inserted_at
      }
  end

  @doc """
  Default settings for a new team
  """
  def default_settings do
    %{
      "reconciliation_access" => "team_only",
      "report_sharing" => true,
      "notification_preferences" => %{
        "team_updates" => true,
        "reconciliation_alerts" => true
      }
    }
  end

  @doc """
  Get setting value with fallback to default
  """
  def get_setting(team, key_path, default \\ nil) do
    settings = team.settings || %{}
    get_in(settings, key_path) || get_in(default_settings(), key_path) || default
  end

  @doc """
  Update a specific setting
  """
  def update_setting(team, key_path, value) do
    current_settings = team.settings || %{}
    new_settings = put_in(current_settings, key_path, value)
    
    team
    |> changeset(%{settings: new_settings})
  end
end
