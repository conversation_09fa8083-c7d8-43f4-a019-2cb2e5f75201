defmodule Reconciliation.Accounts.Permission do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Accounts.Role

  @resources [
    "users", "organizations", "teams", "roles", "reconciliations", 
    "reports", "settings", "activity_logs", "system"
  ]

  @actions ["create", "read", "update", "delete", "manage"]

  schema "permissions" do
    field :name, :string
    field :resource, :string
    field :action, :string
    field :description, :string

    many_to_many :roles, Role, join_through: "role_permissions"

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(permission, attrs) do
    permission
    |> cast(attrs, [:name, :resource, :action, :description])
    |> validate_required([:name, :resource, :action])
    |> validate_length(:name, min: 2, max: 100)
    |> validate_length(:description, max: 500)
    |> validate_inclusion(:resource, @resources)
    |> validate_inclusion(:action, @actions)
    |> unique_constraint(:name)
    |> unique_constraint([:resource, :action])
    |> maybe_generate_name()
  end

  defp maybe_generate_name(changeset) do
    case get_field(changeset, :name) do
      nil ->
        resource = get_field(changeset, :resource)
        action = get_field(changeset, :action)
        if resource && action do
          name = "#{resource}:#{action}"
          put_change(changeset, :name, name)
        else
          changeset
        end
      _ ->
        changeset
    end
  end

  @doc """
  Returns all valid resources
  """
  def resources, do: @resources

  @doc """
  Returns all valid actions
  """
  def actions, do: @actions

  @doc """
  Query permissions by resource
  """
  def by_resource(query \\ __MODULE__, resource) do
    from permission in query, where: permission.resource == ^resource
  end

  @doc """
  Query permissions by action
  """
  def by_action(query \\ __MODULE__, action) do
    from permission in query, where: permission.action == ^action
  end

  @doc """
  Search permissions by name or description
  """
  def search(query \\ __MODULE__, term) do
    search_term = "%#{term}%"
    from permission in query,
      where: ilike(permission.name, ^search_term) or
             ilike(permission.description, ^search_term)
  end

  @doc """
  Create default permissions for all resource-action combinations
  """
  def create_default_permissions do
    permissions = for resource <- @resources,
                      action <- @actions,
                      valid_combination?(resource, action) do
      %{
        name: "#{resource}:#{action}",
        resource: resource,
        action: action,
        description: get_permission_description(resource, action),
        inserted_at: DateTime.utc_now() |> DateTime.truncate(:second),
        updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
      }
    end

    permissions
  end

  defp valid_combination?(resource, action) do
    case {resource, action} do
      {"activity_logs", action} when action in ["create", "update", "delete"] -> false
      {"system", action} when action != "manage" -> false
      _ -> true
    end
  end

  defp get_permission_description(resource, action) do
    case {resource, action} do
      {"users", "create"} -> "Create new users"
      {"users", "read"} -> "View user information"
      {"users", "update"} -> "Update user information"
      {"users", "delete"} -> "Delete users"
      
      {"organizations", "create"} -> "Create new organizations"
      {"organizations", "read"} -> "View organization information"
      {"organizations", "update"} -> "Update organization settings"
      {"organizations", "delete"} -> "Delete organizations"
      
      {"teams", "create"} -> "Create new teams"
      {"teams", "read"} -> "View team information"
      {"teams", "update"} -> "Update team settings"
      {"teams", "delete"} -> "Delete teams"
      
      {"roles", "create"} -> "Create custom roles"
      {"roles", "read"} -> "View role information"
      {"roles", "update"} -> "Update role permissions"
      {"roles", "delete"} -> "Delete custom roles"
      
      {"reconciliations", "create"} -> "Create new reconciliations"
      {"reconciliations", "read"} -> "View reconciliation data"
      {"reconciliations", "update"} -> "Update reconciliation settings"
      {"reconciliations", "delete"} -> "Delete reconciliations"
      
      {"reports", "create"} -> "Generate new reports"
      {"reports", "read"} -> "View existing reports"
      {"reports", "update"} -> "Modify report settings"
      {"reports", "delete"} -> "Delete reports"
      
      {"settings", "create"} -> "Create new settings"
      {"settings", "read"} -> "View system settings"
      {"settings", "update"} -> "Modify system settings"
      {"settings", "delete"} -> "Delete settings"
      
      {"activity_logs", "read"} -> "View activity logs and audit trails"
      
      {"system", "manage"} -> "Full system administration access"
      
      _ -> "#{String.capitalize(action)} #{resource}"
    end
  end

  @doc """
  Get permissions grouped by resource
  """
  def grouped_by_resource do
    from permission in __MODULE__,
      order_by: [permission.resource, permission.action],
      select: %{
        resource: permission.resource,
        action: permission.action,
        name: permission.name,
        description: permission.description
      }
  end

  @doc """
  Check if permission exists for resource and action
  """
  def exists?(resource, action) do
    from(permission in __MODULE__,
      where: permission.resource == ^resource and permission.action == ^action,
      select: count(permission.id)
    )
    |> Reconciliation.Repo.one()
    |> case do
      0 -> false
      _ -> true
    end
  end

  @doc """
  Get permission by resource and action
  """
  def get_by_resource_action(resource, action) do
    from(permission in __MODULE__,
      where: permission.resource == ^resource and permission.action == ^action
    )
    |> Reconciliation.Repo.one()
  end

  @doc """
  Get all permissions for a list of resources
  """
  def for_resources(resources) when is_list(resources) do
    from permission in __MODULE__,
      where: permission.resource in ^resources,
      order_by: [permission.resource, permission.action]
  end
end
