defmodule Reconciliation.Accounts.Role do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Accounts.{Permission, UserRoleAssignment}

  @system_roles ["admin", "manager", "analyst", "viewer"]

  schema "roles" do
    field :name, :string
    field :description, :string
    field :permissions, :map
    field :is_system_role, :boolean

    many_to_many :permissions_list, Permission, join_through: "role_permissions"
    has_many :user_role_assignments, UserRoleAssignment

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(role, attrs) do
    role
    |> cast(attrs, [:name, :description, :permissions, :is_system_role])
    |> validate_required([:name])
    |> validate_length(:name, min: 2, max: 50)
    |> validate_length(:description, max: 500)
    |> unique_constraint(:name)
    |> validate_system_role()
  end

  defp validate_system_role(changeset) do
    name = get_field(changeset, :name)
    is_system_role = get_field(changeset, :is_system_role)

    cond do
      name in @system_roles and not is_system_role ->
        add_error(changeset, :is_system_role, "must be true for system role #{name}")
      name not in @system_roles and is_system_role ->
        add_error(changeset, :is_system_role, "cannot be true for non-system role")
      true ->
        changeset
    end
  end

  @doc """
  Returns all system role names
  """
  def system_roles, do: @system_roles

  @doc """
  Query for system roles
  """
  def system_roles_query(query \\ __MODULE__) do
    from role in query, where: role.is_system_role == true
  end

  @doc """
  Query for custom roles
  """
  def custom_roles_query(query \\ __MODULE__) do
    from role in query, where: role.is_system_role == false
  end

  @doc """
  Query for roles by name
  """
  def by_name(query \\ __MODULE__, name) do
    from role in query, where: role.name == ^name
  end

  @doc """
  Default permissions for system roles
  """
  def default_permissions do
    %{
      "admin" => %{
        "users" => ["create", "read", "update", "delete"],
        "organizations" => ["create", "read", "update", "delete"],
        "teams" => ["create", "read", "update", "delete"],
        "roles" => ["create", "read", "update", "delete"],
        "reconciliations" => ["create", "read", "update", "delete"],
        "reports" => ["create", "read", "update", "delete"],
        "settings" => ["create", "read", "update", "delete"],
        "activity_logs" => ["read"],
        "system" => ["manage"]
      },
      "manager" => %{
        "users" => ["read", "update"],
        "teams" => ["create", "read", "update"],
        "reconciliations" => ["create", "read", "update", "delete"],
        "reports" => ["create", "read", "update"],
        "settings" => ["read", "update"],
        "activity_logs" => ["read"]
      },
      "analyst" => %{
        "reconciliations" => ["create", "read", "update"],
        "reports" => ["create", "read"],
        "settings" => ["read"],
        "activity_logs" => ["read"]
      },
      "viewer" => %{
        "reconciliations" => ["read"],
        "reports" => ["read"],
        "activity_logs" => ["read"]
      }
    }
  end

  @doc """
  Get permissions for a role
  """
  def get_permissions(role) do
    role.permissions || Map.get(default_permissions(), role.name, %{})
  end

  @doc """
  Check if role has permission for resource and action
  """
  def has_permission?(role, resource, action) do
    permissions = get_permissions(role)
    resource_permissions = Map.get(permissions, resource, [])
    action in resource_permissions
  end

  @doc """
  Create system roles with default permissions
  """
  def create_system_roles do
    default_perms = default_permissions()
    
    Enum.map(@system_roles, fn role_name ->
      %{
        name: role_name,
        description: get_role_description(role_name),
        permissions: Map.get(default_perms, role_name, %{}),
        is_system_role: true,
        inserted_at: DateTime.utc_now() |> DateTime.truncate(:second),
        updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
      }
    end)
  end

  defp get_role_description("admin"), do: "Full system administrator with all permissions"
  defp get_role_description("manager"), do: "Team manager with user and reconciliation management permissions"
  defp get_role_description("analyst"), do: "Reconciliation analyst with create and update permissions"
  defp get_role_description("viewer"), do: "Read-only access to assigned reconciliations and reports"

  @doc """
  Check if role is system role
  """
  def system_role?(role) do
    role.is_system_role || role.name in @system_roles
  end

  @doc """
  Get role hierarchy level (lower number = higher privilege)
  """
  def hierarchy_level(role) do
    case role.name do
      "admin" -> 1
      "manager" -> 2
      "analyst" -> 3
      "viewer" -> 4
      _ -> 5  # Custom roles have lowest privilege by default
    end
  end

  @doc """
  Check if role1 has higher privilege than role2
  """
  def higher_privilege?(role1, role2) do
    hierarchy_level(role1) < hierarchy_level(role2)
  end

  @doc """
  Get all permissions that can be assigned to roles
  """
  def available_permissions do
    %{
      "users" => ["create", "read", "update", "delete"],
      "organizations" => ["create", "read", "update", "delete"],
      "teams" => ["create", "read", "update", "delete"],
      "roles" => ["create", "read", "update", "delete"],
      "reconciliations" => ["create", "read", "update", "delete"],
      "reports" => ["create", "read", "update", "delete"],
      "settings" => ["create", "read", "update", "delete"],
      "activity_logs" => ["read"],
      "system" => ["manage"]
    }
  end

  @doc """
  Validate permissions structure
  """
  def validate_permissions(permissions) when is_map(permissions) do
    available = available_permissions()
    
    Enum.all?(permissions, fn {resource, actions} ->
      resource in Map.keys(available) and
      is_list(actions) and
      Enum.all?(actions, &(&1 in Map.get(available, resource, [])))
    end)
  end

  def validate_permissions(_), do: false
end
