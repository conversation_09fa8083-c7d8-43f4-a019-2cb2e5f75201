defmodule Reconciliation.Accounts.UserActivityLog do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Accounts.User
  alias Reconciliation.Organizations.Organization

  @activity_types [
    "authentication",
    "reconciliation", 
    "system",
    "data_access",
    "user_management",
    "security"
  ]

  @actions %{
    "authentication" => ["login", "logout", "password_change", "password_reset", "failed_login", "account_locked"],
    "reconciliation" => ["file_upload", "reconciliation_start", "reconciliation_complete", "export_data", "view_results", "debug_transaction"],
    "system" => ["settings_change", "role_assignment", "user_create", "user_update", "user_delete", "organization_change"],
    "data_access" => ["dashboard_view", "report_generate", "report_export", "transaction_view", "data_filter", "search", "page_visit"],
    "user_management" => ["profile_update", "password_change", "notification_settings", "session_terminate"],
    "security" => ["permission_change", "role_change", "security_setting_change", "suspicious_activity"]
  }

  schema "user_activity_logs" do
    field :session_id, :string
    field :activity_type, :string
    field :action, :string
    field :resource_type, :string
    field :resource_id, :integer
    field :ip_address, :string
    field :user_agent, :string
    field :request_path, :string
    field :request_method, :string
    field :response_status, :integer
    field :duration_ms, :integer
    field :metadata, :map

    belongs_to :user, User
    belongs_to :organization, Organization

    timestamps(type: :utc_datetime, updated_at: false)
  end

  @doc false
  def changeset(user_activity_log, attrs) do
    user_activity_log
    |> cast(attrs, [
      :user_id, :session_id, :activity_type, :action, :resource_type, :resource_id,
      :ip_address, :user_agent, :request_path, :request_method, :response_status,
      :duration_ms, :metadata, :organization_id
    ])
    |> validate_required([:activity_type, :action])
    |> validate_inclusion(:activity_type, @activity_types)
    |> validate_action_for_activity_type()
    |> validate_number(:response_status, greater_than_or_equal_to: 100, less_than: 600)
    |> validate_number(:duration_ms, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:organization_id)
  end

  defp validate_action_for_activity_type(changeset) do
    activity_type = get_field(changeset, :activity_type)
    action = get_field(changeset, :action)

    if activity_type && action do
      valid_actions = Map.get(@actions, activity_type, [])
      if action in valid_actions do
        changeset
      else
        add_error(changeset, :action, "is not valid for activity type #{activity_type}")
      end
    else
      changeset
    end
  end

  @doc """
  Returns all valid activity types
  """
  def activity_types, do: @activity_types

  @doc """
  Returns valid actions for a given activity type
  """
  def actions_for_type(activity_type), do: Map.get(@actions, activity_type, [])

  @doc """
  Returns all valid actions
  """
  def all_actions, do: @actions

  @doc """
  Query for filtering activity logs by user
  """
  def by_user(query \\ __MODULE__, user_id) do
    from log in query, where: log.user_id == ^user_id
  end

  @doc """
  Query for filtering activity logs by organization
  """
  def by_organization(query \\ __MODULE__, organization_id)

  def by_organization(query, organization_id) when not is_nil(organization_id) do
    from log in query, where: log.organization_id == ^organization_id
  end

  def by_organization(query, nil) do
    from log in query, where: is_nil(log.organization_id)
  end

  @doc """
  Query for filtering activity logs by activity type
  """
  def by_activity_type(query \\ __MODULE__, activity_type) do
    from log in query, where: log.activity_type == ^activity_type
  end

  @doc """
  Query for filtering activity logs by action
  """
  def by_action(query \\ __MODULE__, action) do
    from log in query, where: log.action == ^action
  end

  @doc """
  Query for filtering activity logs by date range
  """
  def by_date_range(query \\ __MODULE__, start_date, end_date) do
    from log in query,
      where: log.inserted_at >= ^start_date and log.inserted_at <= ^end_date
  end

  @doc """
  Query for filtering activity logs by resource
  """
  def by_resource(query \\ __MODULE__, resource_type, resource_id) do
    from log in query,
      where: log.resource_type == ^resource_type and log.resource_id == ^resource_id
  end

  @doc """
  Query for filtering activity logs by session
  """
  def by_session(query \\ __MODULE__, session_id) do
    from log in query, where: log.session_id == ^session_id
  end

  @doc """
  Query for recent activity logs
  """
  def recent(query \\ __MODULE__, limit \\ 50) do
    from log in query,
      order_by: [desc: log.inserted_at],
      limit: ^limit
  end

  @doc """
  Query for failed activities (4xx, 5xx status codes)
  """
  def failed_activities(query \\ __MODULE__) do
    from log in query,
      where: log.response_status >= 400
  end

  @doc """
  Query for suspicious activities
  """
  def suspicious_activities(query \\ __MODULE__) do
    from log in query,
      where: log.activity_type == "security" or 
             log.action in ["failed_login", "account_locked"] or
             log.response_status in [401, 403, 429]
  end

  @doc """
  Query with user and organization preloaded
  """
  def with_associations(query \\ __MODULE__) do
    from log in query,
      preload: [:user, :organization]
  end

  @doc """
  Get activity summary for a user within a date range
  """
  def activity_summary(user_id, start_date, end_date) do
    from log in __MODULE__,
      where: log.user_id == ^user_id and 
             log.inserted_at >= ^start_date and 
             log.inserted_at <= ^end_date,
      group_by: [log.activity_type, log.action],
      select: %{
        activity_type: log.activity_type,
        action: log.action,
        count: count(log.id)
      }
  end

  @doc """
  Get activity timeline for a user
  """
  def activity_timeline(user_id, limit \\ 100) do
    from log in __MODULE__,
      where: log.user_id == ^user_id,
      order_by: [desc: log.inserted_at],
      limit: ^limit,
      select: %{
        id: log.id,
        activity_type: log.activity_type,
        action: log.action,
        resource_type: log.resource_type,
        resource_id: log.resource_id,
        inserted_at: log.inserted_at,
        metadata: log.metadata
      }
  end
end
