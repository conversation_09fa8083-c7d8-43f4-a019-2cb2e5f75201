defmodule Reconciliation.Accounts.UserProfile do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Accounts.User

  @timezones [
    "UTC", "America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles",
    "Europe/London", "Europe/Paris", "Europe/Berlin", "Europe/Rome", "Europe/Madrid",
    "Asia/Tokyo", "Asia/Shanghai", "Asia/Kolkata", "Asia/Dubai", "Australia/Sydney",
    "Africa/Cairo", "Africa/Johannesburg", "Africa/Lagos", "Africa/Nairobi"
  ]

  @languages [
    "en", "es", "fr", "de", "it", "pt", "ru", "zh", "ja", "ko", "ar", "hi"
  ]

  schema "user_profiles" do
    field :first_name, :string
    field :last_name, :string
    field :phone_number, :string
    field :job_title, :string
    field :department, :string
    field :profile_picture_url, :string
    field :timezone, :string
    field :language, :string
    field :notification_preferences, :map

    belongs_to :user, User

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(user_profile, attrs) do
    user_profile
    |> cast(attrs, [
      :user_id, :first_name, :last_name, :phone_number, :job_title, :department,
      :profile_picture_url, :timezone, :language, :notification_preferences
    ])
    |> validate_required([:user_id])
    |> validate_length(:first_name, min: 1, max: 50)
    |> validate_length(:last_name, min: 1, max: 50)
    |> validate_length(:job_title, max: 100)
    |> validate_length(:department, max: 100)
    |> validate_phone_number()
    |> validate_inclusion(:timezone, @timezones)
    |> validate_inclusion(:language, @languages)
    |> validate_profile_picture_url()
    |> validate_notification_preferences()
    |> unique_constraint(:user_id)
    |> foreign_key_constraint(:user_id)
  end

  defp validate_phone_number(changeset) do
    case get_change(changeset, :phone_number) do
      nil -> changeset
      phone ->
        # Basic phone number validation - adjust regex as needed
        if Regex.match?(~r/^\+?[\d\s\-\(\)]{7,20}$/, phone) do
          changeset
        else
          add_error(changeset, :phone_number, "must be a valid phone number")
        end
    end
  end

  defp validate_profile_picture_url(changeset) do
    case get_change(changeset, :profile_picture_url) do
      nil -> changeset
      url ->
        if String.match?(url, ~r/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i) do
          changeset
        else
          add_error(changeset, :profile_picture_url, "must be a valid image URL")
        end
    end
  end

  defp validate_notification_preferences(changeset) do
    case get_change(changeset, :notification_preferences) do
      nil -> changeset
      prefs when is_map(prefs) ->
        if valid_notification_preferences?(prefs) do
          changeset
        else
          add_error(changeset, :notification_preferences, "contains invalid preferences")
        end
      _ ->
        add_error(changeset, :notification_preferences, "must be a map")
    end
  end

  defp valid_notification_preferences?(prefs) do
    valid_keys = [
      "email_notifications", "reconciliation_complete", "security_alerts",
      "weekly_reports", "system_maintenance", "team_updates"
    ]
    
    Enum.all?(prefs, fn {key, value} ->
      key in valid_keys and is_boolean(value)
    end)
  end

  @doc """
  Returns available timezones
  """
  def timezones, do: @timezones

  @doc """
  Returns available languages
  """
  def languages, do: @languages

  @doc """
  Default notification preferences
  """
  def default_notification_preferences do
    %{
      "email_notifications" => true,
      "reconciliation_complete" => true,
      "security_alerts" => true,
      "weekly_reports" => false,
      "system_maintenance" => true,
      "team_updates" => true
    }
  end

  @doc """
  Get full name from profile
  """
  def full_name(profile) do
    case {profile.first_name, profile.last_name} do
      {nil, nil} -> nil
      {first, nil} -> first
      {nil, last} -> last
      {first, last} -> "#{first} #{last}"
    end
  end

  @doc """
  Get display name (full name or email fallback)
  """
  def display_name(profile, user \\ nil) do
    case full_name(profile) do
      nil -> if user, do: user.email, else: "Unknown User"
      name -> name
    end
  end

  @doc """
  Get initials from profile
  """
  def initials(profile) do
    case {profile.first_name, profile.last_name} do
      {nil, nil} -> "?"
      {first, nil} -> String.first(first) |> String.upcase()
      {nil, last} -> String.first(last) |> String.upcase()
      {first, last} -> 
        (String.first(first) <> String.first(last)) |> String.upcase()
    end
  end

  @doc """
  Check if profile is complete
  """
  def complete?(profile) do
    required_fields = [:first_name, :last_name, :timezone, :language]
    Enum.all?(required_fields, &(Map.get(profile, &1) not in [nil, ""]))
  end

  @doc """
  Get notification preference value with fallback
  """
  def get_notification_preference(profile, key, default \\ true) do
    preferences = profile.notification_preferences || default_notification_preferences()
    Map.get(preferences, key, default)
  end

  @doc """
  Update notification preference
  """
  def update_notification_preference(profile, key, value) do
    current_prefs = profile.notification_preferences || default_notification_preferences()
    new_prefs = Map.put(current_prefs, key, value)
    
    profile
    |> changeset(%{notification_preferences: new_prefs})
  end

  @doc """
  Query profiles by search term (name, job title, department)
  """
  def search(query \\ __MODULE__, term) do
    search_term = "%#{term}%"
    from profile in query,
      where: ilike(profile.first_name, ^search_term) or
             ilike(profile.last_name, ^search_term) or
             ilike(profile.job_title, ^search_term) or
             ilike(profile.department, ^search_term)
  end

  @doc """
  Query profiles by department
  """
  def by_department(query \\ __MODULE__, department) do
    from profile in query, where: profile.department == ^department
  end

  @doc """
  Query profiles with user preloaded
  """
  def with_user(query \\ __MODULE__) do
    from profile in query, preload: [:user]
  end

  @doc """
  Get profile completion percentage
  """
  def completion_percentage(profile) do
    total_fields = 8  # first_name, last_name, phone_number, job_title, department, profile_picture_url, timezone, language
    completed_fields = [
      profile.first_name,
      profile.last_name,
      profile.phone_number,
      profile.job_title,
      profile.department,
      profile.profile_picture_url,
      profile.timezone,
      profile.language
    ]
    |> Enum.count(&(&1 not in [nil, ""]))

    round(completed_fields / total_fields * 100)
  end
end
