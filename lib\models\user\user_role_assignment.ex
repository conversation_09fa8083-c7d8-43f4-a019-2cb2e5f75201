defmodule Reconciliation.Accounts.UserRoleAssignment do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Accounts.{User, Role}
  alias Reconciliation.Organizations.Organization

  schema "user_role_assignments" do
    field :assigned_at, :utc_datetime
    field :expires_at, :utc_datetime

    belongs_to :user, User
    belongs_to :role, Role
    belongs_to :organization, Organization
    belongs_to :assigned_by, User, foreign_key: :assigned_by_id

    timestamps(type: :utc_datetime, updated_at: false)
  end

  @doc false
  def changeset(user_role_assignment, attrs) do
    user_role_assignment
    |> cast(attrs, [:user_id, :role_id, :organization_id, :assigned_by_id, :assigned_at, :expires_at])
    |> validate_required([:user_id, :role_id, :assigned_at])
    |> validate_expiry_date()
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:role_id)
    |> foreign_key_constraint(:organization_id)
    |> foreign_key_constraint(:assigned_by_id)
    |> unique_constraint([:user_id, :role_id, :organization_id])
  end

  defp validate_expiry_date(changeset) do
    assigned_at = get_field(changeset, :assigned_at)
    expires_at = get_field(changeset, :expires_at)

    if assigned_at && expires_at && DateTime.compare(expires_at, assigned_at) != :gt do
      add_error(changeset, :expires_at, "must be after assigned date")
    else
      changeset
    end
  end

  @doc """
  Create a new role assignment
  """
  def create_assignment(user_id, role_id, assigned_by_id, opts \\ []) do
    %{
      user_id: user_id,
      role_id: role_id,
      assigned_by_id: assigned_by_id,
      assigned_at: DateTime.utc_now() |> DateTime.truncate(:second),
      organization_id: Keyword.get(opts, :organization_id),
      expires_at: Keyword.get(opts, :expires_at)
    }
  end

  @doc """
  Query for active role assignments (not expired)
  """
  def active(query \\ __MODULE__) do
    now = DateTime.utc_now()
    from assignment in query,
      where: is_nil(assignment.expires_at) or assignment.expires_at > ^now
  end

  @doc """
  Query for expired role assignments
  """
  def expired(query \\ __MODULE__) do
    now = DateTime.utc_now()
    from assignment in query,
      where: not is_nil(assignment.expires_at) and assignment.expires_at <= ^now
  end

  @doc """
  Query for role assignments by user
  """
  def by_user(query \\ __MODULE__, user_id) do
    from assignment in query, where: assignment.user_id == ^user_id
  end

  @doc """
  Query for role assignments by role
  """
  def by_role(query \\ __MODULE__, role_id) do
    from assignment in query, where: assignment.role_id == ^role_id
  end

  @doc """
  Query for role assignments by organization
  """
  def by_organization(query \\ __MODULE__, organization_id) do
    from assignment in query, where: assignment.organization_id == ^organization_id
  end

  @doc """
  Query for role assignments assigned by a specific user
  """
  def assigned_by(query \\ __MODULE__, assigned_by_id) do
    from assignment in query, where: assignment.assigned_by == ^assigned_by_id
  end

  @doc """
  Query with all associations preloaded
  """
  def with_associations(query \\ __MODULE__) do
    from assignment in query,
      preload: [:user, :role, :organization, :assigned_by]
  end

  @doc """
  Check if role assignment is active (not expired)
  """
  def active?(assignment) do
    case assignment.expires_at do
      nil -> true
      expires_at -> DateTime.compare(expires_at, DateTime.utc_now()) == :gt
    end
  end

  @doc """
  Get user's active roles for an organization
  """
  def user_active_roles(user_id, organization_id \\ nil) do
    query = from assignment in __MODULE__,
      where: assignment.user_id == ^user_id,
      join: role in assoc(assignment, :role),
      select: role

    query = if organization_id do
      from assignment in query,
        where: assignment.organization_id == ^organization_id or is_nil(assignment.organization_id)
    else
      query
    end

    query
    |> active()
    |> Reconciliation.Repo.all()
  end

  @doc """
  Get users with a specific role in an organization
  """
  def users_with_role(role_id, organization_id \\ nil) do
    query = from assignment in __MODULE__,
      where: assignment.role_id == ^role_id,
      join: user in assoc(assignment, :user),
      select: user

    query = if organization_id do
      from assignment in query,
        where: assignment.organization_id == ^organization_id
    else
      query
    end

    query
    |> active()
    |> Reconciliation.Repo.all()
  end

  @doc """
  Expire a role assignment
  """
  def expire_assignment(assignment) do
    assignment
    |> changeset(%{expires_at: DateTime.utc_now() |> DateTime.truncate(:second)})
  end

  @doc """
  Extend role assignment expiry
  """
  def extend_assignment(assignment, new_expiry_date) do
    assignment
    |> changeset(%{expires_at: new_expiry_date})
  end

  @doc """
  Get role assignment statistics for an organization
  """
  def organization_statistics(organization_id) do
    from assignment in __MODULE__,
      where: assignment.organization_id == ^organization_id,
      join: role in assoc(assignment, :role),
      group_by: role.name,
      select: %{
        role_name: role.name,
        active_assignments: count(assignment.id, :distinct),
        total_users: count(assignment.user_id, :distinct)
      }
  end
end
