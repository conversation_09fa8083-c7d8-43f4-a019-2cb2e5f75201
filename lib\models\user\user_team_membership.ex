defmodule Reconciliation.Accounts.UserTeamMembership do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Accounts.User
  alias Reconciliation.Organizations.Team

  @roles ["member", "lead"]

  schema "user_team_memberships" do
    field :role, :string

    belongs_to :user, User
    belongs_to :team, Team

    timestamps(type: :utc_datetime, updated_at: false)
  end

  @doc false
  def changeset(user_team_membership, attrs) do
    user_team_membership
    |> cast(attrs, [:user_id, :team_id, :role])
    |> validate_required([:user_id, :team_id, :role])
    |> validate_inclusion(:role, @roles)
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:team_id)
    |> unique_constraint([:user_id, :team_id])
  end

  @doc """
  Returns all valid roles
  """
  def roles, do: @roles

  @doc """
  Query memberships by user
  """
  def by_user(query \\ __MODULE__, user_id) do
    from membership in query, where: membership.user_id == ^user_id
  end

  @doc """
  Query memberships by team
  """
  def by_team(query \\ __MODULE__, team_id) do
    from membership in query, where: membership.team_id == ^team_id
  end

  @doc """
  Query memberships by role
  """
  def by_role(query \\ __MODULE__, role) do
    from membership in query, where: membership.role == ^role
  end

  @doc """
  Query team leads
  """
  def team_leads(query \\ __MODULE__) do
    from membership in query, where: membership.role == "lead"
  end

  @doc """
  Query with associations preloaded
  """
  def with_associations(query \\ __MODULE__) do
    from membership in query, preload: [:user, :team]
  end

  @doc """
  Check if user is team lead
  """
  def team_lead?(membership) do
    membership.role == "lead"
  end

  @doc """
  Get user's teams
  """
  def user_teams(user_id) do
    from membership in __MODULE__,
      where: membership.user_id == ^user_id,
      join: team in assoc(membership, :team),
      select: team
  end

  @doc """
  Get team members
  """
  def team_members(team_id) do
    from membership in __MODULE__,
      where: membership.team_id == ^team_id,
      join: user in assoc(membership, :user),
      select: %{user: user, role: membership.role, joined_at: membership.inserted_at}
  end

  @doc """
  Get team leads for a team
  """
  def get_team_leads(team_id) do
    from membership in __MODULE__,
      where: membership.team_id == ^team_id and membership.role == "lead",
      join: user in assoc(membership, :user),
      select: user
  end

  @doc """
  Check if user is member of team
  """
  def member_of_team?(user_id, team_id) do
    from(membership in __MODULE__,
      where: membership.user_id == ^user_id and membership.team_id == ^team_id,
      select: count(membership.id)
    )
    |> Reconciliation.Repo.one()
    |> case do
      0 -> false
      _ -> true
    end
  end

  @doc """
  Check if user is lead of team
  """
  def lead_of_team?(user_id, team_id) do
    from(membership in __MODULE__,
      where: membership.user_id == ^user_id and 
             membership.team_id == ^team_id and 
             membership.role == "lead",
      select: count(membership.id)
    )
    |> Reconciliation.Repo.one()
    |> case do
      0 -> false
      _ -> true
    end
  end

  @doc """
  Promote user to team lead
  """
  def promote_to_lead(membership) do
    membership
    |> changeset(%{role: "lead"})
  end

  @doc """
  Demote user to team member
  """
  def demote_to_member(membership) do
    membership
    |> changeset(%{role: "member"})
  end
end
