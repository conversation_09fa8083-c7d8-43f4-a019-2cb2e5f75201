defmodule Reconciliation.Emails do
  @moduledoc """
  Email functions for the Reconciliation system.
  """
  
  import Bamboo.Email
  alias Reconciliation.Mailer

  @doc """
  Sends a password reset email with reset link.
  """
  def password_reset_email(user, reset_url) do
    new_email()
    |> from("<EMAIL>")
    |> to(user.email)
    |> subject("Reset your password - ProBASE Reconciliation")
    |> html_body("""
    <h2>Password Reset Request</h2>
    <p>Dear #{if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"},</p>
    <p>We received a password reset request for your ProBASE Reconciliation account (#{user.email}).</p>
    <p>Click the link below to reset your password:</p>
    <p><a href="#{reset_url}" style="background-color: #4ade80; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
    <p>If you didn't request this reset, please ignore this email.</p>
    <p>Best regards,<br>ProBASE Reconciliation Team</p>
    """)
    |> text_body("""
    Password Reset Request

    Dear #{if user.profile && user.profile.first_name, do: user.profile.first_name, else: "User"},

    We received a password reset request for your ProBASE Reconciliation account (#{user.email}).

    Click the link below to reset your password:
    #{reset_url}

    If you didn't request this reset, please ignore this email.

    Best regards,
    ProBASE Reconciliation Team
    """)
  end

  @doc """
  Sends a welcome email to a new user with their temporary password.
  """
  def welcome_email(user, temporary_password, login_url) do
    new_email()
    |> from("<EMAIL>")
    |> to(user.email)
    |> subject("Welcome to ProBASE Reconciliation - Your Account is Ready")
    |> html_body("""
    <h2>Welcome to ProBASE Reconciliation!</h2>
    <p>Dear User,</p>
    <p>Your ProBASE Reconciliation account has been created successfully!</p>
    <p><strong>Account Details:</strong></p>
    <ul>
      <li>Email: #{user.email}</li>
      <li>Temporary Password: <strong>#{temporary_password}</strong></li>
    </ul>
    <p>Click the link below to log in and get started:</p>
    <p><a href="#{login_url}" style="background-color: #4ade80; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Log In Now</a></p>
    <p><strong>Important:</strong> Please change your password after your first login for security.</p>
    <p>If you have any questions or need assistance, please contact your administrator.</p>
    <p>Best regards,<br>ProBASE Reconciliation Team</p>
    """)
    |> text_body("""
    Welcome to ProBASE Reconciliation!

    Dear User,

    Your ProBASE Reconciliation account has been created successfully!

    Account Details:
    - Email: #{user.email}
    - Temporary Password: #{temporary_password}

    Log in at: #{login_url}

    Important: Please change your password after your first login for security.

    If you have any questions or need assistance, please contact your administrator.

    Best regards,
    ProBASE Reconciliation Team
    """)
  end

  @doc """
  Delivers an email using the configured mailer.
  """
  def deliver(email) do
    try do
      result = Mailer.deliver_now(email)
      {:ok, result}
    rescue
      error ->
        {:error, error}
    end
  end
end
