defmodule Reconciliation.PasswordGenerator do
  @moduledoc """
  Utility module for generating secure random passwords.
  """

  @doc """
  Generates a secure random password that meets the application's requirements.
  
  The generated password will:
  - Be at least 12 characters long (meets minimum requirement)
  - Contain uppercase letters
  - Contain lowercase letters  
  - Contain numbers
  - Contain special characters
  
  ## Examples
  
      iex> Reconciliation.PasswordGenerator.generate()
      "Kx9#mP2$vL8@"
      
      iex> Reconciliation.PasswordGenerator.generate(16)
      "Kx9#mP2$vL8@Qw3!"
  """
  def generate(length \\ 12) when length >= 12 do
    # Ensure we have at least one character from each required category
    uppercase = Enum.random(?A..?Z)
    lowercase = Enum.random(?a..?z)
    number = Enum.random(?0..?9)
    special = Enum.random(~c"!@#$%^&*")

    # Generate remaining characters from all categories
    remaining_length = length - 4
    uppercase_chars = Enum.to_list(?A..?Z)
    lowercase_chars = Enum.to_list(?a..?z)
    number_chars = Enum.to_list(?0..?9)
    special_chars = ~c"!@#$%^&*"
    all_chars = uppercase_chars ++ lowercase_chars ++ number_chars ++ special_chars

    remaining_chars =
      for _ <- 1..remaining_length do
        Enum.random(all_chars)
      end

    # Combine all characters and shuffle
    [uppercase, lowercase, number, special | remaining_chars]
    |> Enum.shuffle()
    |> List.to_string()
  end
  
  @doc """
  Generates a temporary password for new users.
  This is a convenience function that generates a 12-character password.
  """
  def generate_temporary_password do
    generate(12)
  end
end
