defmodule Reconciliation.Reconciliation do
  @moduledoc """
  The Reconciliation context.
  """

  import Ecto.Query, warn: false
  alias Reconciliation.Repo

  require Logger # Added this line

  alias Reconciliation.{
    ReconciliationRun,
    UploadedFile,
    Transaction,
    TransactionMatch,
    ReconciliationSettings,
    Accounts
  }
  alias Reconciliation.Services.ActivityLogger

  ## Reconciliation Runs

  @doc """
  Returns the list of reconciliation runs for a user.
  """
  def list_reconciliation_runs(user_id) do
    ReconciliationRun
    |> where([r], r.user_id == ^user_id)
    |> order_by([r], desc: r.inserted_at)
    |> preload([:uploaded_files, :user])
    |> Repo.all()
  end

  @doc """
  Returns the count of pending reconciliation runs for a user.
  """
  def count_pending_reconciliation_runs(user_id) do
    ReconciliationRun
    |> where([r], r.user_id == ^user_id and r.status == "pending")
    |> Repo.aggregate(:count, :id)
  end

  @doc """
  Gets a single reconciliation run.
  """
  def get_reconciliation_run!(id) do
    ReconciliationRun
    |> preload([:uploaded_files, :transactions, :transaction_matches, :user])
    |> Repo.get!(id)
  end

  @doc """
  Gets a single reconciliation run, returns nil if not found.
  """
  def get_reconciliation_run(id) do
    ReconciliationRun
    |> preload([:uploaded_files, :transactions, :transaction_matches, :user])
    |> Repo.get(id)
  end

  @doc """
  Gets a reconciliation run by id and user_id.
  """
  def get_user_reconciliation_run(id, user_id) do
    ReconciliationRun
    |> where([r], r.id == ^id and r.user_id == ^user_id)
    |> preload([:uploaded_files, :transactions, :transaction_matches, :user])
    |> Repo.one()
  end

  @doc """
  Creates a reconciliation run.
  """
  def create_reconciliation_run(attrs \\ %{}) do
    %ReconciliationRun{}
    |> ReconciliationRun.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Generates a user-friendly name for reconciliation run based on uploaded files and date.
  Includes a unique identifier to prevent conflicts when same files are uploaded multiple times.
  """
  def generate_run_name_from_files(file_a_name, file_b_name) do
    name_a = extract_meaningful_name(file_a_name)
    name_b = extract_meaningful_name(file_b_name)
    date_str = Date.utc_today() |> Calendar.strftime("%b %d, %Y")
    unique_id = generate_short_unique_id()

    "#{name_a} vs #{name_b} - #{date_str} (#{unique_id})"
  end

  @doc """
  Generates a fallback name when file names are not descriptive.
  Includes unique identifier to prevent conflicts.
  """
  def generate_fallback_run_name() do
    date_str = Date.utc_today() |> Calendar.strftime("%B %d, %Y")
    time_str = Time.utc_now() |> Calendar.strftime("%I:%M %p")
    unique_id = generate_short_unique_id()

    "#{date_str} at #{time_str} - Reconciliation (#{unique_id})"
  end

  @doc """
  Extracts meaningful names from filenames for display purposes.
  """
  def extract_meaningful_name(filename) do
    filename
    |> String.replace(~r/\.(xlsx?|csv)$/i, "")  # Remove file extensions
    |> String.replace(~r/_+/, " ")              # Replace underscores with spaces
    |> String.replace(~r/-+/, " ")              # Replace dashes with spaces
    |> String.replace(~r/\s+/, " ")             # Normalize multiple spaces
    |> String.trim()                            # Remove leading/trailing spaces
    |> case do
      "" -> "File"                              # Fallback for empty names
      name when byte_size(name) > 30 ->
        String.slice(name, 0, 30) <> "..."     # Truncate very long names
      name -> name
    end
    |> String.split(" ")                        # Split into words
    |> Enum.map(&String.capitalize/1)          # Capitalize each word
    |> Enum.join(" ")                          # Join back with spaces
  end

  @doc """
  Generates a short unique identifier for reconciliation runs.
  Uses timestamp + random bytes for uniqueness.
  """
  def generate_short_unique_id() do
    # Get current timestamp in seconds
    timestamp = DateTime.utc_now() |> DateTime.to_unix()

    # Get last 4 digits of timestamp for time-based uniqueness
    time_part = rem(timestamp, 10000) |> Integer.to_string() |> String.pad_leading(4, "0")

    # Generate 2 random bytes for additional uniqueness
    random_part = :crypto.strong_rand_bytes(2) |> Base.encode16(case: :lower)

    # Combine for a short but unique identifier like "1234ab"
    "#{time_part}#{random_part}"
  end



  @doc """
  Updates a reconciliation run.
  """
  def update_reconciliation_run(%ReconciliationRun{} = reconciliation_run, attrs) do
    reconciliation_run
    |> ReconciliationRun.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates reconciliation run statistics.
  """
  def update_reconciliation_stats(%ReconciliationRun{} = reconciliation_run, attrs) do
    reconciliation_run
    |> ReconciliationRun.stats_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Marks a reconciliation run as failed with error message.
  """
  def mark_reconciliation_failed(%ReconciliationRun{} = reconciliation_run, error_message) do
    reconciliation_run
    |> ReconciliationRun.error_changeset(error_message)
    |> Repo.update()
  end

  @doc """
  Deletes a reconciliation run.
  """
  def delete_reconciliation_run(%ReconciliationRun{} = reconciliation_run) do
    Repo.delete(reconciliation_run)
  end

  @doc """
  Marks a reconciliation run as failed by its ID.
  This is useful if the run struct itself cannot be fetched but we need to update its status.
  """
  def mark_reconciliation_failed_by_id(reconciliation_run_id, error_message) do
    case Repo.get(ReconciliationRun, reconciliation_run_id) do
      nil ->
        Logger.error("Attempted to mark run ID #{inspect(reconciliation_run_id)} as failed, but it was not found.")
        {:error, :not_found}
      %ReconciliationRun{} = run ->
        mark_reconciliation_failed(run, error_message)
    end
  end

  ## Uploaded Files

  @doc """
  Creates an uploaded file record.
  """
  def create_uploaded_file(attrs \\ %{}) do
    %UploadedFile{}
    |> UploadedFile.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an uploaded file.
  """
  def update_uploaded_file(%UploadedFile{} = uploaded_file, attrs) do
    uploaded_file
    |> UploadedFile.processing_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Marks an uploaded file as failed with errors.
  """
  def mark_file_failed(%UploadedFile{} = uploaded_file, errors) do
    uploaded_file
    |> UploadedFile.error_changeset(errors)
    |> Repo.update()
  end

  @doc """
  Gets uploaded files for a reconciliation run.
  """
  def get_uploaded_files(reconciliation_run_id) do
    UploadedFile
    |> where([f], f.reconciliation_run_id == ^reconciliation_run_id)
    |> order_by([f], asc: f.file_type)
    |> Repo.all()
  end

  @doc """
  Gets a single uploaded file by ID.
  """
  def get_uploaded_file(id) do
    Repo.get(UploadedFile, id)
  end

  @doc """
  Gets a single uploaded file by ID, raises if not found.
  """
  def get_uploaded_file!(id) do
    Repo.get!(UploadedFile, id)
  end

  ## Transactions

  @doc """
  Creates a transaction.
  """
  def create_transaction(attrs \\ %{}) do
    %Transaction{}
    |> Transaction.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates multiple transactions in a batch.
  """
  def create_transactions(transactions_attrs) do
    Repo.insert_all(Transaction, transactions_attrs, returning: true)
  end

  @doc """
  Updates a transaction.
  """
  def update_transaction(%Transaction{} = transaction, attrs) do
    transaction
    |> Transaction.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates transaction match status.
  """
  def update_transaction_match_status(%Transaction{} = transaction, attrs) do
    transaction
    |> Transaction.match_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Gets transactions for a reconciliation run.
  """
  def get_transactions(reconciliation_run_id) do
    Transaction
    |> where([t], t.reconciliation_run_id == ^reconciliation_run_id)
    |> order_by([t], asc: t.transaction_date, asc: t.row_number)
    |> preload([:uploaded_file])
    |> Repo.all()
  end

  @doc """
  Gets transactions for a specific file.
  """
  def get_file_transactions(uploaded_file_id) do
    Transaction
    |> where([t], t.uploaded_file_id == ^uploaded_file_id)
    |> order_by([t], asc: t.row_number)
    |> Repo.all()
  end

  @doc """
  Gets unmatched transactions for a reconciliation run.
  """
  def get_unmatched_transactions(reconciliation_run_id) do
    Transaction
    |> where([t], t.reconciliation_run_id == ^reconciliation_run_id and t.is_matched == false)
    |> order_by([t], asc: t.transaction_date, asc: t.amount)
    |> preload([:uploaded_file])
    |> Repo.all()
  end

  ## Transaction Matches

  @doc """
  Creates a transaction match.
  """
  def create_transaction_match(attrs \\ %{}) do
    %TransactionMatch{}
    |> TransactionMatch.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a transaction match.
  """
  def update_transaction_match(%TransactionMatch{} = match, attrs) do
    match
    |> TransactionMatch.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Verifies a transaction match.
  """
  def verify_transaction_match(%TransactionMatch{} = match, verified \\ true) do
    match
    |> TransactionMatch.verification_changeset(verified)
    |> Repo.update()
  end

  @doc """
  Gets transaction matches for a reconciliation run.
  """
  def get_transaction_matches(reconciliation_run_id) do
    TransactionMatch
    |> where([m], m.reconciliation_run_id == ^reconciliation_run_id)
    |> order_by([m], desc: m.confidence_score)
    |> preload([transaction_a: :uploaded_file, transaction_b: :uploaded_file])
    |> Repo.all()
  end

  ## Reconciliation Settings

  @doc """
  Gets or creates reconciliation settings for a user.
  """
  def get_or_create_settings(user_id) do
    ReconciliationSettings.get_or_create_for_user(user_id)
  end

  @doc """
  Updates reconciliation settings.
  """
  def update_settings(%ReconciliationSettings{} = settings, attrs) do
    settings
    |> ReconciliationSettings.changeset(attrs)
    |> Repo.update()
  end

  ## Statistics and Analytics

  @doc """
  Gets reconciliation statistics for a run.
  """
  def get_reconciliation_stats(reconciliation_run_id) do
    run = get_reconciliation_run!(reconciliation_run_id)

    # Get primary currency for this run
    primary_currency = get_primary_currency_for_run(reconciliation_run_id)

    # Get currency-specific totals for each file
    currency_totals = get_currency_totals_by_file(reconciliation_run_id)

    %{
      total_transactions_a: run.total_transactions_a,
      total_transactions_b: run.total_transactions_b,
      matched_count: run.matched_count,
      unmatched_a_count: run.unmatched_a_count,
      unmatched_b_count: run.unmatched_b_count,
      total_amount_a: run.total_amount_a,
      total_amount_b: run.total_amount_b,
      difference_amount: run.difference_amount,
      match_rate: run.match_rate,
      status: run.status,
      primary_currency: primary_currency,
      currency_totals_a: currency_totals.file_a,
      currency_totals_b: currency_totals.file_b
    }
  end

  @doc """
  Gets currency-specific totals for each file in a reconciliation run.
  """
  def get_currency_totals_by_file(reconciliation_run_id) do
    transactions = get_transactions(reconciliation_run_id)

    file_a_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_a"
    end)

    file_b_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_b"
    end)

    file_a_totals = calculate_currency_totals(file_a_transactions)
    file_b_totals = calculate_currency_totals(file_b_transactions)

    %{
      file_a: file_a_totals,
      file_b: file_b_totals
    }
  end

  defp calculate_currency_totals(transactions) do
    transactions
    |> Enum.group_by(fn t -> t.currency || "USD" end)
    |> Enum.map(fn {currency, currency_transactions} ->
      total_amount = currency_transactions
        |> Enum.map(&(&1.amount))
        |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

      {currency, %{
        count: length(currency_transactions),
        total: total_amount
      }}
    end)
    |> Enum.into(%{})
  end

  @doc """
  Gets comprehensive summary statistics for all reconciliation data for a user.
  """
  def get_comprehensive_summary(user_id, opts \\ []) do
    date_filter = Keyword.get(opts, :date_filter, :all)
    run_filter = Keyword.get(opts, :run_filter, nil)

    runs = list_reconciliation_runs(user_id)
    filtered_runs = runs
    |> filter_runs_by_date(date_filter)
    |> filter_runs_by_specific_run(run_filter)

    %{
      file_upload_stats: calculate_file_upload_stats(filtered_runs),
      transaction_metrics: calculate_transaction_metrics(filtered_runs),
      summary_info: calculate_summary_info(filtered_runs),
      currency_breakdown: calculate_currency_breakdown(filtered_runs),
      status_overview: calculate_status_overview(filtered_runs),
      date_range: calculate_date_range(filtered_runs)
    }
  end

  @doc """
  Gets file upload statistics across all reconciliation runs.
  """
  def calculate_file_upload_stats(runs) do
    all_files = runs |> Enum.flat_map(& &1.uploaded_files)

    file_type_breakdown =
      all_files
      |> Enum.group_by(& &1.file_type)
      |> Enum.map(fn {type, files} ->
        {type, %{
          count: length(files),
          total_size: Enum.sum(Enum.map(files, & &1.file_size || 0)),
          avg_size: if(length(files) > 0, do: Enum.sum(Enum.map(files, & &1.file_size || 0)) / length(files), else: 0),
          sample_names: files |> Enum.take(3) |> Enum.map(&UploadedFile.display_name/1) |> Enum.uniq()
        }}
      end)
      |> Enum.into(%{})

    %{
      total_files: length(all_files),
      total_size: Enum.sum(Enum.map(all_files, & &1.file_size || 0)),
      file_type_breakdown: file_type_breakdown,
      upload_dates: all_files |> Enum.map(& &1.inserted_at) |> Enum.sort(),
      avg_file_size: if(length(all_files) > 0, do: Enum.sum(Enum.map(all_files, & &1.file_size || 0)) / length(all_files), else: 0)
    }
  end

  @doc """
  Gets transaction matching metrics across all reconciliation runs.
  """
  def calculate_transaction_metrics(runs) do
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))

    # Calculate separate totals for each data source
    total_processed_a = Enum.sum(Enum.map(completed_runs, &(&1.total_transactions_a || 0)))
    total_processed_b = Enum.sum(Enum.map(completed_runs, &(&1.total_transactions_b || 0)))

    # Total transactions processed is the sum of all individual transactions from both files
    total_transactions = total_processed_a + total_processed_b
    total_matched = Enum.sum(Enum.map(completed_runs, &(&1.matched_count || 0)))

    # Calculate unmatched correctly - sum of unmatched from both files
    total_unmatched_a = Enum.sum(Enum.map(completed_runs, &(&1.unmatched_a_count || 0)))
    total_unmatched_b = Enum.sum(Enum.map(completed_runs, &(&1.unmatched_b_count || 0)))
    total_unmatched = total_unmatched_a + total_unmatched_b

    # Match rate should be based on potential matches (pairs), not individual transactions
    max_possible_matches = Enum.sum(Enum.map(completed_runs, &(max(&1.total_transactions_a, &1.total_transactions_b))))
    match_rate = if max_possible_matches > 0, do: Float.round(total_matched / max_possible_matches * 100, 2), else: 0.0

    # Get sample file names for display
    all_files = runs |> Enum.flat_map(& &1.uploaded_files)
    sample_file_a = all_files |> Enum.find(&(&1.file_type == "file_a")) |> case do
      nil -> nil
      file -> UploadedFile.short_display_name(file, 20)
    end
    sample_file_b = all_files |> Enum.find(&(&1.file_type == "file_b")) |> case do
      nil -> nil
      file -> UploadedFile.short_display_name(file, 20)
    end

    %{
      total_processed_a: total_processed_a,
      total_processed_b: total_processed_b,
      total_entries_processed: total_transactions,
      total_entries_matched: total_matched,
      total_entries_unmatched: total_unmatched,
      total_unmatched_a: total_unmatched_a,
      total_unmatched_b: total_unmatched_b,
      max_possible_matches: max_possible_matches,
      match_rate_percentage: match_rate,
      avg_match_rate: calculate_avg_match_rate(completed_runs),
      sample_file_a_name: sample_file_a,
      sample_file_b_name: sample_file_b
    }
  end

  @doc """
  Gets additional summary information.
  """
  def calculate_summary_info(runs) do
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))
    pending_runs = Enum.filter(runs, &(&1.status == "pending"))
    failed_runs = Enum.filter(runs, &(&1.status == "failed"))

    %{
      total_reconciliation_runs: length(runs),
      completed_runs: length(completed_runs),
      pending_runs: length(pending_runs),
      failed_runs: length(failed_runs),
      success_rate: if(length(runs) > 0, do: Float.round(length(completed_runs) / length(runs) * 100, 2), else: 0.0)
    }
  end

  @doc """
  Gets currency breakdown if multiple currencies are supported.
  """
  def calculate_currency_breakdown(runs) do
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))

    # Get all transactions across all runs to analyze currencies by file type
    transactions_by_file_type =
      completed_runs
      |> Enum.flat_map(fn run ->
        # Get transactions for this run with file type information
        from(t in Transaction,
          join: f in UploadedFile, on: t.uploaded_file_id == f.id,
          where: t.reconciliation_run_id == ^run.id,
          select: %{currency: t.currency, amount: t.amount, file_type: f.file_type}
        )
        |> Repo.all()
      end)
      |> Enum.filter(fn transaction ->
        # Only include transactions that have a currency value (from actual file data)
        not is_nil(transaction.currency) and transaction.currency != ""
      end)
      |> Enum.group_by(& &1.file_type)

    # Calculate stats for each file type
    file_type_currency_stats =
      transactions_by_file_type
      |> Enum.map(fn {file_type, transactions} ->
        currency_stats =
          transactions
          |> Enum.group_by(& &1.currency)
          |> Enum.map(fn {currency, currency_transactions} ->
            total_amount =
              currency_transactions
              |> Enum.map(& &1.amount)
              |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

            {currency, %{
              transaction_count: length(currency_transactions),
              total_amount: total_amount
            }}
          end)
          |> Enum.into(%{})

        {file_type, currency_stats}
      end)
      |> Enum.into(%{})

    # Calculate currency differences between File A and File B
    currency_differences = calculate_currency_differences(file_type_currency_stats)

    # Overall currency stats with differences
    all_transactions = Map.values(transactions_by_file_type) |> List.flatten()
    overall_currency_stats =
      all_transactions
      |> Enum.group_by(& &1.currency)
      |> Enum.map(fn {currency, transactions} ->
        total_amount =
          transactions
          |> Enum.map(& &1.amount)
          |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

        # Get difference for this currency
        difference_info = Map.get(currency_differences, currency, %{
          difference: Decimal.new("0"),
          file_a_amount: Decimal.new("0"),
          file_b_amount: Decimal.new("0"),
          file_a_count: 0,
          file_b_count: 0
        })

        {currency, %{
          transaction_count: length(transactions),
          total_amount: total_amount,
          file_a_amount: difference_info.file_a_amount,
          file_b_amount: difference_info.file_b_amount,
          file_a_count: difference_info.file_a_count,
          file_b_count: difference_info.file_b_count,
          difference: difference_info.difference,
          difference_percentage: calculate_difference_percentage(difference_info.difference, difference_info.file_a_amount, difference_info.file_b_amount)
        }}
      end)
      |> Enum.into(%{})

    %{
      currencies_found: Map.keys(overall_currency_stats),
      currency_stats: overall_currency_stats,
      primary_currency: get_primary_currency(overall_currency_stats),
      file_type_breakdown: file_type_currency_stats,
      currency_differences: currency_differences
    }
  end

  @doc """
  Gets status overview of reconciliation runs.
  """
  def calculate_status_overview(runs) do
    status_counts =
      runs
      |> Enum.group_by(& &1.status)
      |> Enum.map(fn {status, runs_list} -> {status, length(runs_list)} end)
      |> Enum.into(%{})

    %{
      status_breakdown: status_counts,
      total_runs: length(runs),
      completion_rate: if(length(runs) > 0, do: Float.round((Map.get(status_counts, "completed", 0) / length(runs)) * 100, 2), else: 0.0)
    }
  end

  @doc """
  Calculates the date range of reconciliation data.
  """
  def calculate_date_range(runs) when length(runs) == 0, do: %{earliest: nil, latest: nil}
  def calculate_date_range(runs) do
    dates = Enum.map(runs, & &1.inserted_at)
    %{
      earliest: Enum.min(dates),
      latest: Enum.max(dates)
    }
  end

  # Private helper functions

  defp filter_runs_by_date(runs, :all), do: runs
  defp filter_runs_by_date(runs, :last_30_days) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-30, :day)
    Enum.filter(runs, &(DateTime.compare(&1.inserted_at, cutoff_date) == :gt))
  end
  defp filter_runs_by_date(runs, :last_90_days) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-90, :day)
    Enum.filter(runs, &(DateTime.compare(&1.inserted_at, cutoff_date) == :gt))
  end
  defp filter_runs_by_date(runs, :last_year) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-365, :day)
    Enum.filter(runs, &(DateTime.compare(&1.inserted_at, cutoff_date) == :gt))
  end
  defp filter_runs_by_date(runs, :today) do
    today = DateTime.utc_now() |> DateTime.to_date()
    Enum.filter(runs, fn run ->
      run_date = DateTime.to_date(run.inserted_at)
      Date.compare(run_date, today) == :eq
    end)
  end
  defp filter_runs_by_date(runs, :yesterday) do
    yesterday = DateTime.utc_now() |> DateTime.add(-1, :day) |> DateTime.to_date()
    Enum.filter(runs, fn run ->
      run_date = DateTime.to_date(run.inserted_at)
      Date.compare(run_date, yesterday) == :eq
    end)
  end
  defp filter_runs_by_date(runs, :last_7_days) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-7, :day)
    Enum.filter(runs, &(DateTime.compare(&1.inserted_at, cutoff_date) == :gt))
  end
  defp filter_runs_by_date(runs, :this_month) do
    now = DateTime.utc_now()
    start_of_month = %{now | day: 1, hour: 0, minute: 0, second: 0, microsecond: {0, 0}}
    Enum.filter(runs, &(DateTime.compare(&1.inserted_at, start_of_month) != :lt))
  end
  defp filter_runs_by_date(runs, :last_month) do
    now = DateTime.utc_now()
    start_of_this_month = %{now | day: 1, hour: 0, minute: 0, second: 0, microsecond: {0, 0}}
    start_of_last_month = start_of_this_month |> DateTime.add(-1, :month)
    end_of_last_month = start_of_this_month |> DateTime.add(-1, :second)

    Enum.filter(runs, fn run ->
      DateTime.compare(run.inserted_at, start_of_last_month) != :lt and
      DateTime.compare(run.inserted_at, end_of_last_month) != :gt
    end)
  end
  defp filter_runs_by_date(runs, :this_year) do
    now = DateTime.utc_now()
    start_of_year = %{now | month: 1, day: 1, hour: 0, minute: 0, second: 0, microsecond: {0, 0}}
    Enum.filter(runs, &(DateTime.compare(&1.inserted_at, start_of_year) != :lt))
  end
  defp filter_runs_by_date(runs, {start_date, end_date}) do
    Enum.filter(runs, fn run ->
      DateTime.compare(run.inserted_at, start_date) != :lt and
      DateTime.compare(run.inserted_at, end_date) != :gt
    end)
  end

  # Filter by specific reconciliation run ID
  defp filter_runs_by_specific_run(runs, nil), do: runs
  defp filter_runs_by_specific_run(runs, run_id) do
    Enum.filter(runs, fn run -> run.id == run_id end)
  end

  defp calculate_avg_match_rate([]), do: 0.0
  defp calculate_avg_match_rate(completed_runs) do
    completed_runs
    |> Enum.map(&Decimal.to_float(&1.match_rate))
    |> Enum.sum()
    |> Kernel./(length(completed_runs))
    |> Float.round(2)
  end

  defp get_primary_currency(currency_stats) when map_size(currency_stats) == 0, do: nil
  defp get_primary_currency(currency_stats) do
    currency_stats
    |> Enum.max_by(fn {_currency, stats} -> stats.transaction_count end)
    |> elem(0)
  end

  defp get_primary_currency_for_run(reconciliation_run_id) do
    # Get all transactions for this run that have currency data
    transactions = from(t in Transaction,
      where: t.reconciliation_run_id == ^reconciliation_run_id
        and not is_nil(t.currency)
        and t.currency != "",
      select: %{currency: t.currency}
    )
    |> Repo.all()

    # Group by currency and count
    currency_stats = transactions
    |> Enum.group_by(& &1.currency)
    |> Enum.map(fn {currency, currency_transactions} ->
      {currency, %{transaction_count: length(currency_transactions)}}
    end)
    |> Enum.into(%{})

    get_primary_currency(currency_stats)
  end

  defp calculate_currency_differences(file_type_currency_stats) do
    file_a_stats = Map.get(file_type_currency_stats, "file_a", %{})
    file_b_stats = Map.get(file_type_currency_stats, "file_b", %{})

    # Get all currencies from both files
    all_currencies = MapSet.union(
      MapSet.new(Map.keys(file_a_stats)),
      MapSet.new(Map.keys(file_b_stats))
    )

    all_currencies
    |> Enum.map(fn currency ->
      file_a_data = Map.get(file_a_stats, currency, %{total_amount: Decimal.new("0"), transaction_count: 0})
      file_b_data = Map.get(file_b_stats, currency, %{total_amount: Decimal.new("0"), transaction_count: 0})

      file_a_amount = file_a_data.total_amount
      file_b_amount = file_b_data.total_amount
      difference = Decimal.sub(file_a_amount, file_b_amount)

      {currency, %{
        file_a_amount: file_a_amount,
        file_b_amount: file_b_amount,
        file_a_count: file_a_data.transaction_count,
        file_b_count: file_b_data.transaction_count,
        difference: difference,
        difference_percentage: calculate_difference_percentage(difference, file_a_amount, file_b_amount)
      }}
    end)
    |> Enum.into(%{})
  end

  defp calculate_difference_percentage(difference, file_a_amount, file_b_amount) do
    # Calculate percentage difference based on the larger amount to avoid division by zero
    larger_amount = Decimal.max(Decimal.abs(file_a_amount), Decimal.abs(file_b_amount))

    if Decimal.equal?(larger_amount, Decimal.new("0")) do
      Decimal.new("0")
    else
      difference
      |> Decimal.abs()
      |> Decimal.div(larger_amount)
      |> Decimal.mult(Decimal.new("100"))
      |> Decimal.round(2)
    end
  end

  @doc """
  Calculates and updates reconciliation run statistics.
  """
  def calculate_reconciliation_stats(reconciliation_run_id) do
    # Get all transactions for this run
    transactions = get_transactions(reconciliation_run_id)

    # Separate by file type
    file_a_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_a"
    end)

    file_b_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_b"
    end)

    # Calculate totals
    total_a = length(file_a_transactions)
    total_b = length(file_b_transactions)

    # Count matched transactions per file (each match involves one transaction from each file)
    matched_a_count = Enum.count(file_a_transactions, &(&1.is_matched))
    matched_b_count = Enum.count(file_b_transactions, &(&1.is_matched))

    # The number of matches is the minimum of matched transactions from each file
    # (since each match pairs one transaction from A with one from B)
    matched_count = min(matched_a_count, matched_b_count)

    unmatched_a = total_a - matched_a_count
    unmatched_b = total_b - matched_b_count

    # Calculate amounts
    amount_a = file_a_transactions
    |> Enum.map(&(&1.amount))
    |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

    amount_b = file_b_transactions
    |> Enum.map(&(&1.amount))
    |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

    difference = Decimal.sub(amount_a, amount_b) |> Decimal.abs()

    # Calculate match rate
    total_transactions = max(total_a, total_b)
    match_rate = ReconciliationRun.calculate_match_rate(total_transactions, matched_count)

    # Update the reconciliation run
    run = get_reconciliation_run!(reconciliation_run_id)
    case update_reconciliation_stats(run, %{
      total_transactions_a: total_a,
      total_transactions_b: total_b,
      matched_count: matched_count,
      unmatched_a_count: unmatched_a,
      unmatched_b_count: unmatched_b,
      total_amount_a: amount_a,
      total_amount_b: amount_b,
      difference_amount: difference,
      match_rate: match_rate,
      status: "completed",
      processed_at: DateTime.utc_now()
    }) do
      {:ok, updated_run} ->
        # Log the successful update
        require Logger
        Logger.info("Reconciliation run #{updated_run.id} completed successfully. Broadcasting update to user #{updated_run.user_id}")

        # Log reconciliation completion activity
        # Get user to access organization_id
        user = Accounts.get_user!(updated_run.user_id)
        ActivityLogger.log_reconciliation_activity(
          updated_run.user_id,
          "reconciliation_complete",
          resource_type: "reconciliation_run",
          resource_id: updated_run.id,
          organization_id: user.organization_id,
          metadata: %{
            run_name: updated_run.name,
            total_transactions_a: total_a,
            total_transactions_b: total_b,
            matched_count: matched_count,
            match_rate: match_rate,
            difference_amount: Decimal.to_string(difference)
          }
        )

        # Broadcast completion to update dashboard in real-time
        case Phoenix.PubSub.broadcast(
          Reconciliation.PubSub,
          "reconciliation_updates:#{updated_run.user_id}",
          {:reconciliation_completed, updated_run.id}
        ) do
          :ok ->
            Logger.info("Successfully broadcasted reconciliation completion for run #{updated_run.id}")
          {:error, reason} ->
            Logger.error("Failed to broadcast reconciliation completion: #{inspect(reason)}")
        end

        {:ok, updated_run}

      {:error, changeset} ->
        require Logger
        Logger.error("Failed to update reconciliation stats: #{inspect(changeset.errors)}")
        {:error, changeset}
    end
  end
end
