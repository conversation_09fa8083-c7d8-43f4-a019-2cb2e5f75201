<%= if @current_user do %>
  <style>
    /* White background for logged-in users only */
    body {
      background: #ffffff !important;
      color: #1f2937 !important;
    }

    /* Ensure cards are light gray on white background */
    .bg-gray-800 {
      background-color: #f9fafb !important;
      border-color: #e5e7eb !important;
    }

    .bg-white {
      background-color: #f9fafb !important;
      border-color: #e5e7eb !important;
    }

    /* Fix text colors for light theme */
    .text-white {
      color: #1f2937 !important;
    }

    .text-gray-300 {
      color: #6b7280 !important;
    }
  </style>
  <div class="flex h-screen" style="background: #ffffff;">
    <.live_component module={ReconciliationWeb.SidebarComponent} id="sidebar" current_user={@current_user} current_path={assigns[:current_path] || "/"} />

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col overflow-hidden" style="background: #ffffff;">
      <.live_component module={ReconciliationWeb.NavbarComponent} id="navbar" current_user={@current_user} />

      <!-- Page Content -->
      <main class="flex-1 overflow-x-hidden overflow-y-auto py-4" style="background: #ffffff;">
        <div class="max-w-6xl mx-auto">
          <.flash_group flash={@flash} />
          <%= @inner_content %>
        </div>
      </main>
    </div>
  </div>
<% else %>
  <!-- Layout for logged-out users (original app.html.heex content) -->
  <main class="px-4 py-20 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-2xl">
      <.flash_group flash={@flash} />
      <%= @inner_content %>
    </div>
  </main>
<% end %>
