defmodule ReconciliationWeb.UserSessionController do
  use ReconciliationWeb, :controller

  alias Reconciliation.Accounts
  alias ReconciliationWeb.UserAuth
  alias Reconciliation.Services.ActivityLogger

  def create(conn, %{"_action" => "registered"} = params) do
    create(conn, params, "Account created successfully!")
  end

  def create(conn, %{"_action" => "password_updated"} = params) do
    conn
    |> put_session(:user_return_to, ~p"/users/settings")
    |> create(params, "Password updated successfully!")
  end

  def create(conn, params) do
    create(conn, params, "Welcome back!")
  end

  defp create(conn, %{"user" => user_params}, info) do
    %{"email" => email, "password" => password} = user_params

    if user = Accounts.get_user_by_email_and_password(email, password) do
      # Log successful login
      ActivityLogger.log_activity_from_conn(conn, user.id, "authentication", "login",
        response_status: 200,
        organization_id: user.organization_id,
        metadata: %{login_method: "email_password"}
      )

      conn
      |> put_flash(:info, info)
      |> UserAuth.log_in_user(user, user_params)
    else
      # Log failed login attempt
      ActivityLogger.log_activity_from_conn(conn, nil, "authentication", "failed_login",
        response_status: 401,
        metadata: %{attempted_email: email, failure_reason: "invalid_credentials"}
      )

      # In order to prevent user enumeration attacks, don't disclose whether the email is registered.
      conn
      |> put_flash(:error, "Invalid email or password")
      |> put_flash(:email, String.slice(email, 0, 160))
      |> redirect(to: ~p"/users/log_in")
    end
  end

  def delete(conn, _params) do
    conn
    |> put_flash(:info, "Logged out successfully.")
    |> UserAuth.log_out_user()
  end
end
