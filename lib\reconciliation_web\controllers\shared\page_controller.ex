defmodule ReconciliationWeb.PageController do
  use ReconciliationWeb, :controller

  def redirect_to_login(conn, _params) do
    # Check if user is already authenticated
    if conn.assigns[:current_user] do
      # If logged in, redirect to dashboard
      redirect(conn, to: ~p"/dashboard")
    else
      # If not logged in, redirect to login page
      redirect(conn, to: ~p"/users/log_in")
    end
  end

  def home(conn, _params) do
    # The home page is often custom made,
    # so skip the default app layout.
    render(conn, :home, layout: false)
  end
end
