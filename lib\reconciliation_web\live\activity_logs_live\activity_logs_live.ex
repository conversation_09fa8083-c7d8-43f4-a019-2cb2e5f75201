defmodule ReconciliationWeb.ActivityLogsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.Services.ActivityLogger
  alias Reconciliation.Accounts.UserActivityLog

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user
    activities = get_user_activities(user.id)

    socket =
      socket
      |> assign(:page_title, "My Activity Log")
      |> assign(:activities, activities)
      |> assign(:activity_type_filter, "all")
      |> assign(:date_range, "all")
      |> assign(:filtered_activities, activities)

    {:ok, socket}
  end

  @impl true
  def handle_event("filter_activities", %{"activity_type" => activity_type, "date_range" => date_range}, socket) do
    filtered_activities = filter_activities(socket.assigns.activities, activity_type, date_range)

    socket =
      socket
      |> assign(:activity_type_filter, activity_type)
      |> assign(:date_range, date_range)
      |> assign(:filtered_activities, filtered_activities)

    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">My Activity Log</h1>
        <p class="mt-2 text-gray-600">Track your recent activities and actions in the reconciliation system.</p>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Filter Activities</h3>
        
        <form phx-change="filter_activities" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Activity Type Filter -->
          <div>
            <label for="activity_type" class="block text-sm font-medium text-gray-700 mb-2">Activity Type</label>
            <select name="activity_type" id="activity_type" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="all" selected={@activity_type_filter == "all"}>All Activities</option>
              <option value="authentication" selected={@activity_type_filter == "authentication"}>Authentication</option>
              <option value="reconciliation" selected={@activity_type_filter == "reconciliation"}>Reconciliation</option>
              <option value="data_access" selected={@activity_type_filter == "data_access"}>Data Access</option>
              <option value="system" selected={@activity_type_filter == "system"}>System</option>
              <option value="user_management" selected={@activity_type_filter == "user_management"}>User Management</option>
            </select>
          </div>

          <!-- Date Range Filter -->
          <div>
            <label for="date_range" class="block text-sm font-medium text-gray-700 mb-2">Time Period</label>
            <select name="date_range" id="date_range" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="all" selected={@date_range == "all"}>All Activities</option>
              <option value="recent" selected={@date_range == "recent"}>Recent (Last 24 hours)</option>
              <option value="yesterday" selected={@date_range == "yesterday"}>Yesterday</option>
              <option value="last3days" selected={@date_range == "last3days"}>Last 3 Days</option>
            </select>
          </div>
        </form>
      </div>

      <!-- Activity Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-blue-600"><%= count_activities_by_type(@filtered_activities, "authentication") %></div>
          <div class="text-sm text-gray-500">Authentication</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-green-600"><%= count_activities_by_type(@filtered_activities, "reconciliation") %></div>
          <div class="text-sm text-gray-500">Reconciliation</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-orange-600"><%= count_activities_by_type(@filtered_activities, "data_access") %></div>
          <div class="text-sm text-gray-500">Data Access</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-purple-600"><%= count_activities_by_type(@filtered_activities, "system") %></div>
          <div class="text-sm text-gray-500">System</div>
        </div>
      </div>

      <!-- Activities Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">Recent Activities</h3>
          <p class="text-sm text-gray-600 mt-1">Showing <%= length(@filtered_activities) %> activities</p>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= for activity <- @filtered_activities do %>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= Calendar.strftime(activity.inserted_at, "%Y-%m-%d %H:%M:%S") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={activity_type_badge_class(activity.activity_type)}>
                      <%= String.capitalize(activity.activity_type) %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= format_action(activity.action) %>
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    <%= format_activity_details(activity) %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <%= if length(@filtered_activities) == 0 do %>
          <div class="px-6 py-12 text-center">
            <div class="text-gray-400 mb-2">
              <.icon name="hero-clipboard-document-list" class="w-12 h-12 mx-auto" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No activities found</h3>
            <p class="text-gray-500">Try adjusting your filters to see more activities.</p>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  # Helper functions
  defp get_user_activities(user_id) do
    ActivityLogger.get_user_activities(user_id, 100)
  end

  defp filter_activities(activities, "all", date_range) do
    filter_by_date_range(activities, date_range)
  end
  defp filter_activities(activities, activity_type, date_range) do
    activities
    |> Enum.filter(&(&1.activity_type == activity_type))
    |> filter_by_date_range(date_range)
  end

  defp filter_by_date_range(activities, "all"), do: activities
  defp filter_by_date_range(activities, "recent") do
    twenty_four_hours_ago = DateTime.utc_now() |> DateTime.add(-24, :hour)
    Enum.filter(activities, fn activity ->
      DateTime.compare(activity.inserted_at, twenty_four_hours_ago) != :lt
    end)
  end
  defp filter_by_date_range(activities, "yesterday") do
    yesterday = Date.utc_today() |> Date.add(-1)
    Enum.filter(activities, fn activity ->
      Date.compare(DateTime.to_date(activity.inserted_at), yesterday) == :eq
    end)
  end
  defp filter_by_date_range(activities, "last3days") do
    three_days_ago = Date.utc_today() |> Date.add(-3)
    Enum.filter(activities, fn activity ->
      Date.compare(DateTime.to_date(activity.inserted_at), three_days_ago) != :lt
    end)
  end

  defp count_activities_by_type(activities, type) do
    Enum.count(activities, &(&1.activity_type == type))
  end

  defp activity_type_badge_class("authentication"), do: "inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
  defp activity_type_badge_class("reconciliation"), do: "inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
  defp activity_type_badge_class("data_access"), do: "inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
  defp activity_type_badge_class("system"), do: "inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
  defp activity_type_badge_class("user_management"), do: "inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
  defp activity_type_badge_class("security"), do: "inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
  defp activity_type_badge_class(_), do: "inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"

  defp format_action(action) do
    action
    |> String.replace("_", " ")
    |> String.split(" ")
    |> Enum.map(&String.capitalize/1)
    |> Enum.join(" ")
  end

  defp format_activity_details(activity) do
    case activity.metadata do
      %{"page" => page, "url" => url} when is_binary(page) and is_binary(url) ->
        "Page: #{String.capitalize(String.replace(page, "_", " "))} (#{url})"
      %{"filename" => filename} when is_binary(filename) ->
        "File: #{filename}"
      %{"run_name" => run_name} when is_binary(run_name) ->
        "Run: #{run_name}"
      %{"search_query" => query} when is_binary(query) ->
        "Search: \"#{query}\""
      %{"filter_type" => filter_type, "filter_value" => filter_value} ->
        "Filter: #{filter_type} = #{filter_value}"
      %{"export_type" => export_type, "transaction_count" => count} ->
        "Export: #{export_type} (#{count} transactions)"
      _ ->
        if activity.resource_type do
          "Resource: #{activity.resource_type}"
        else
          "-"
        end
    end
  end
end
