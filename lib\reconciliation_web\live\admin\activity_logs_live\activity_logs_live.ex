defmodule ReconciliationWeb.Admin.ActivityLogsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.UserManagement
  alias Reconciliation.Services.ActivityLogger
  alias Reconciliation.Accounts.UserActivityLog

  @impl true
  def mount(_params, _session, socket) do
    # Check if user has permission to view activity logs
    if can_view_activity_logs?(socket.assigns.current_user) do
      activities = get_activities_for_user(socket.assigns.current_user)

      socket =
        socket
        |> assign(:page_title, "Activity Logs")
        |> assign(:activities, activities)
        |> assign(:activity_type_filter, "all")
        |> assign(:user_filter, "all")
        |> assign(:date_range, "today")

      {:ok, socket}
    else
      socket =
        socket
        |> put_flash(:error, "You don't have permission to access this page.")
        |> redirect(to: ~p"/dashboard")

      {:ok, socket}
    end
  end

  @impl true
  def handle_event("filter_activity_type", %{"activity_type" => activity_type}, socket) do
    activities = get_filtered_activities(activity_type, socket.assigns.user_filter, socket.assigns.date_range, socket.assigns.current_user)

    socket =
      socket
      |> assign(:activities, activities)
      |> assign(:activity_type_filter, activity_type)

    {:noreply, socket}
  end

  def handle_event("filter_user", %{"user" => user_filter}, socket) do
    activities = get_filtered_activities(socket.assigns.activity_type_filter, user_filter, socket.assigns.date_range, socket.assigns.current_user)

    socket =
      socket
      |> assign(:activities, activities)
      |> assign(:user_filter, user_filter)

    {:noreply, socket}
  end

  def handle_event("filter_date_range", %{"date_range" => date_range}, socket) do
    activities = get_filtered_activities(socket.assigns.activity_type_filter, socket.assigns.user_filter, date_range, socket.assigns.current_user)

    socket =
      socket
      |> assign(:activities, activities)
      |> assign(:date_range, date_range)

    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Activity Logs</h1>
          <p class="text-gray-600">Monitor user activities and system events</p>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Activity Type Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Activity Type</label>
            <select phx-change="filter_activity_type" name="activity_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
              <option value="all" selected={@activity_type_filter == "all"}>All Types</option>
              <option value="authentication" selected={@activity_type_filter == "authentication"}>Authentication</option>
              <option value="reconciliation" selected={@activity_type_filter == "reconciliation"}>Reconciliation</option>
              <option value="system" selected={@activity_type_filter == "system"}>System</option>
              <option value="data_access" selected={@activity_type_filter == "data_access"}>Data Access</option>
              <option value="user_management" selected={@activity_type_filter == "user_management"}>User Management</option>
              <option value="security" selected={@activity_type_filter == "security"}>Security</option>
            </select>
          </div>

          <!-- User Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">User</label>
            <select phx-change="filter_user" name="user" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
              <option value="all" selected={@user_filter == "all"}>All Users</option>
              <!-- Add user options dynamically if needed -->
            </select>
          </div>

          <!-- Date Range Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
            <select phx-change="filter_date_range" name="date_range" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
              <option value="today" selected={@date_range == "today"}>Today</option>
              <option value="week" selected={@date_range == "week"}>This Week</option>
              <option value="month" selected={@date_range == "month"}>This Month</option>
              <option value="all" selected={@date_range == "all"}>All Time</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Activity Logs Table -->
      <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= for activity <- @activities do %>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= Calendar.strftime(activity.inserted_at, "%Y-%m-%d %H:%M:%S") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      <%= if activity.user do %>
                        <%= activity.user.email %>
                      <% else %>
                        System
                      <% end %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <span class={activity_type_badge_class(activity.activity_type)}>
                        <%= String.capitalize(activity.activity_type) %>
                      </span>
                      <span class="ml-2 text-sm text-gray-600">
                        <%= activity.action %>
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    <div class="max-w-xs truncate">
                      <%= if activity.resource_type do %>
                        <%= activity.resource_type %>
                        <%= if activity.resource_id, do: "##{activity.resource_id}" %>
                      <% end %>
                      <%= if activity.metadata && map_size(activity.metadata) > 0 do %>
                        <div class="text-xs text-gray-400 mt-1">
                          <%= format_metadata(activity.metadata) %>
                        </div>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= activity.ip_address || "N/A" %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <%= if activity.response_status do %>
                      <span class={status_badge_class(activity.response_status)}>
                        <%= activity.response_status %>
                      </span>
                    <% else %>
                      <span class="text-gray-400">N/A</span>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-gray-900"><%= length(@activities) %></div>
          <div class="text-sm text-gray-500">Total Activities</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-green-600"><%= count_activities_by_type(@activities, "authentication") %></div>
          <div class="text-sm text-gray-500">Authentication</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-blue-600"><%= count_activities_by_type(@activities, "reconciliation") %></div>
          <div class="text-sm text-gray-500">Reconciliation</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-purple-600"><%= count_activities_by_type(@activities, "system") %></div>
          <div class="text-sm text-gray-500">System</div>
        </div>
      </div>
    </div>
    """
  end

  # Helper functions
  defp can_view_activity_logs?(user) do
    # Only admins should be able to see what users are doing
    UserManagement.user_has_role?(user, "admin")
  end

  defp get_activities_for_user(user) do
    # Admins should see all activities regardless of organization
    if UserManagement.user_has_role?(user, "admin") do
      get_all_recent_activities(100)
    else
      case user.organization_id do
        nil ->
          ActivityLogger.get_user_activities(user.id, 100)
        organization_id ->
          ActivityLogger.get_organization_activities(organization_id, 100)
      end
    end
  end

  defp get_filtered_activities(activity_type, user_filter, date_range, current_user) do
    # For now, just return activities for the user
    # In a real implementation, you'd apply the filters
    get_activities_for_user(current_user)
  end

  defp get_all_recent_activities(limit) do
    # Get recent activities across all organizations for admin users
    UserActivityLog
    |> UserActivityLog.recent(limit)
    |> UserActivityLog.with_associations()
    |> Reconciliation.Repo.all()
  end

  defp activity_type_badge_class("authentication"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
  defp activity_type_badge_class("reconciliation"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
  defp activity_type_badge_class("system"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
  defp activity_type_badge_class("data_access"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
  defp activity_type_badge_class("user_management"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
  defp activity_type_badge_class("security"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
  defp activity_type_badge_class(_), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"

  defp status_badge_class(status) when status >= 200 and status < 300, do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
  defp status_badge_class(status) when status >= 300 and status < 400, do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
  defp status_badge_class(status) when status >= 400 and status < 500, do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
  defp status_badge_class(status) when status >= 500, do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
  defp status_badge_class(_), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"

  defp count_activities_by_type(activities, type) do
    Enum.count(activities, &(&1.activity_type == type))
  end

  defp format_metadata(metadata) when is_map(metadata) do
    metadata
    |> Enum.take(2)  # Show only first 2 items
    |> Enum.map(fn {k, v} -> "#{k}: #{format_metadata_value(v)}" end)
    |> Enum.join(", ")
  end

  defp format_metadata(_), do: ""

  # Helper function to format metadata values
  defp format_metadata_value(value) when is_map(value) do
    # Handle nested maps like status_change: %{"from" => "active", "to" => "inactive"}
    value
    |> Enum.map(fn {k, v} -> "#{k}=#{v}" end)
    |> Enum.join(", ")
  end

  defp format_metadata_value(value) when is_list(value) do
    # Handle lists
    Enum.join(value, ", ")
  end

  defp format_metadata_value(value), do: to_string(value)
end
