defmodule ReconciliationWeb.Admin.OrganizationsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.UserManagement
  alias Reconciliation.Organizations.Organization

  @impl true
  def mount(_params, _session, socket) do
    if can_manage_organizations?(socket.assigns.current_user) do
      organizations = UserManagement.list_organizations()

      socket =
        socket
        |> assign(:page_title, "Organization Management")
        |> assign(:organizations, organizations)
        |> assign(:show_form, false)
        |> assign(:editing_org, nil)
        |> assign(:form, nil)
        |> assign(:search_term, "")
        |> assign(:status_filter, "all")

      {:ok, socket}
    else
      socket =
        socket
        |> put_flash(:error, "You don't have permission to access this page.")
        |> redirect(to: ~p"/dashboard")

      {:ok, socket}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Organization Management")
  end

  defp apply_action(socket, action, _params) do
    socket
    |> assign(:page_title, "Organization Management - #{String.capitalize(to_string(action))}")
  end

  @impl true
  def handle_event("new_organization", _params, socket) do
    changeset = Organization.changeset(%Organization{}, %{})
    form = to_form(changeset)

    {:noreply,
     socket
     |> assign(:show_form, true)
     |> assign(:editing_org, nil)
     |> assign(:form, form)
    }
  end

  def handle_event("edit_organization", %{"id" => id}, socket) do
    organization = Enum.find(socket.assigns.organizations, &(&1.id == String.to_integer(id)))
    changeset = Organization.changeset(organization, %{})
    form = to_form(changeset)

    {:noreply,
     socket
     |> assign(:show_form, true)
     |> assign(:editing_org, organization)
     |> assign(:form, form)
    }
  end

  def handle_event("cancel_form", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_form, false)
     |> assign(:editing_org, nil)
     |> assign(:form, nil)
    }
  end

  def handle_event("validate", %{"organization" => org_params}, socket) do
    organization = socket.assigns.editing_org || %Organization{}
    changeset = Organization.changeset(organization, org_params)
    form = to_form(changeset, action: :validate)

    {:noreply, assign(socket, :form, form)}
  end

  def handle_event("save", %{"organization" => org_params}, socket) do
    case socket.assigns.editing_org do
      nil ->
        # Create new organization
        case UserManagement.create_organization(org_params) do
          {:ok, _organization} ->
            organizations = UserManagement.list_organizations()
            {:noreply,
             socket
             |> assign(:organizations, organizations)
             |> assign(:show_form, false)
             |> assign(:form, nil)
             |> put_flash(:info, "Organization created successfully.")
            }
          {:error, changeset} ->
            form = to_form(changeset)
            {:noreply, assign(socket, :form, form)}
        end

      organization ->
        # Update existing organization
        case UserManagement.update_organization(organization, org_params) do
          {:ok, _organization} ->
            organizations = UserManagement.list_organizations()
            {:noreply,
             socket
             |> assign(:organizations, organizations)
             |> assign(:show_form, false)
             |> assign(:editing_org, nil)
             |> assign(:form, nil)
             |> put_flash(:info, "Organization updated successfully.")
            }
          {:error, changeset} ->
            form = to_form(changeset)
            {:noreply, assign(socket, :form, form)}
        end
    end
  end

  def handle_event("delete_organization", %{"id" => id}, socket) do
    organization = Enum.find(socket.assigns.organizations, &(&1.id == String.to_integer(id)))

    case UserManagement.delete_organization(organization) do
      {:ok, _} ->
        organizations = UserManagement.list_organizations()
        {:noreply,
         socket
         |> assign(:organizations, organizations)
         |> put_flash(:info, "Organization deleted successfully.")
        }
      {:error, _} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete organization.")
        }
    end
  end

  def handle_event("search", %{"search" => term}, socket) do
    {:noreply, assign(socket, :search_term, term)}
  end

  def handle_event("filter_status", %{"status" => status}, socket) do
    {:noreply, assign(socket, :status_filter, status)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Organization Management</h1>
          <p class="text-gray-600">Manage organizations and their settings</p>
        </div>
        <button
          phx-click="new_organization"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium"
        >
          New Organization
        </button>
      </div>

      <!-- Search and Filter Controls -->
      <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <form phx-change="search">
              <input
                type="text"
                name="search"
                value={@search_term}
                placeholder="Search organizations..."
                class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </form>
          </div>
          <div class="sm:w-48">
            <form phx-change="filter_status">
              <select
                name="status"
                class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all" selected={@status_filter == "all"}>All Status</option>
                <option value="active" selected={@status_filter == "active"}>Active</option>
                <option value="inactive" selected={@status_filter == "inactive"}>Inactive</option>
                <option value="suspended" selected={@status_filter == "suspended"}>Suspended</option>
              </select>
            </form>
          </div>
        </div>
      </div>

      <!-- Organizations Table -->
      <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Organizations</h3>
        </div>

        <%= if Enum.empty?(filtered_organizations(assigns)) do %>
          <div class="p-8 text-center">
            <div class="text-gray-500 mb-4">
              <.icon name="hero-building-office" class="w-12 h-12 mx-auto" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No organizations found</h3>
            <p class="text-gray-600">Get started by creating your first organization.</p>
          </div>
        <% else %>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Organization
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for org <- filtered_organizations(assigns) do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900"><%= org.name %></div>
                        <div class="text-sm text-gray-500"><%= org.slug %></div>
                        <%= if org.description do %>
                          <div class="text-sm text-gray-600 mt-1"><%= org.description %></div>
                        <% end %>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={[
                        "inline-flex px-2 py-1 text-xs font-semibold rounded-full",
                        status_badge_class(org.status)
                      ]}>
                        <%= String.capitalize(org.status) %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= format_date(org.inserted_at) %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div class="flex justify-end space-x-2">
                        <button
                          phx-click="edit_organization"
                          phx-value-id={org.id}
                          class="text-blue-600 hover:text-blue-900"
                        >
                          Edit
                        </button>
                        <button
                          phx-click="delete_organization"
                          phx-value-id={org.id}
                          data-confirm="Are you sure you want to delete this organization?"
                          class="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% end %>
      </div>

      <!-- Organization Form Modal -->
      <%= if @show_form do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                <%= if @editing_org, do: "Edit Organization", else: "New Organization" %>
              </h3>

              <.form for={@form} phx-change="validate" phx-submit="save" class="space-y-4">
                <div>
                  <.input field={@form[:name]} type="text" label="Name" required />
                </div>

                <div>
                  <.input field={@form[:slug]} type="text" label="Slug" />
                  <p class="text-xs text-gray-500 mt-1">URL-friendly identifier (lowercase, numbers, hyphens only)</p>
                </div>

                <div>
                  <.input field={@form[:description]} type="textarea" label="Description" rows="3" />
                </div>

                <div>
                  <.input field={@form[:status]} type="select" label="Status" options={[
                    {"Active", "active"},
                    {"Inactive", "inactive"},
                    {"Suspended", "suspended"}
                  ]} />
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    phx-click="cancel_form"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
                  >
                    <%= if @editing_org, do: "Update", else: "Create" %>
                  </button>
                </div>
              </.form>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper functions
  defp filtered_organizations(assigns) do
    organizations = assigns.organizations
    search_term = String.downcase(assigns.search_term || "")
    status_filter = assigns.status_filter

    organizations
    |> filter_by_search(search_term)
    |> filter_by_status(status_filter)
  end

  defp filter_by_search(organizations, ""), do: organizations
  defp filter_by_search(organizations, search_term) do
    Enum.filter(organizations, fn org ->
      String.contains?(String.downcase(org.name), search_term) ||
      String.contains?(String.downcase(org.slug || ""), search_term) ||
      String.contains?(String.downcase(org.description || ""), search_term)
    end)
  end

  defp filter_by_status(organizations, "all"), do: organizations
  defp filter_by_status(organizations, status) do
    Enum.filter(organizations, &(&1.status == status))
  end

  defp status_badge_class("active"), do: "bg-green-100 text-green-800"
  defp status_badge_class("inactive"), do: "bg-gray-100 text-gray-800"
  defp status_badge_class("suspended"), do: "bg-red-100 text-red-800"
  defp status_badge_class(_), do: "bg-gray-100 text-gray-800"

  defp format_date(nil), do: "-"
  defp format_date(date), do: Calendar.strftime(date, "%b %d, %Y")

  # Permission check functions
  defp can_manage_organizations?(user) do
    # Check if user has admin role or organization management permissions
    UserManagement.user_has_role?(user, "admin") ||
    UserManagement.user_has_permission?(user, "organizations", "manage")
  end
end
