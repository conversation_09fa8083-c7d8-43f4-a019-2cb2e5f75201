defmodule ReconciliationWeb.Admin.RolesLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.UserManagement

  @impl true
  def mount(_params, _session, socket) do
    if can_manage_roles?(socket.assigns.current_user) do
      roles = UserManagement.list_roles()
      users = UserManagement.list_users()

      socket =
        socket
        |> assign(:page_title, "Role Management")
        |> assign(:roles, roles)
        |> assign(:users, users)
        |> assign(:show_create_form, false)
        |> assign(:show_assign_form, false)
        |> assign(:selected_role, nil)
        |> assign(:selected_user, nil)
        |> assign(:form_errors, %{})

      {:ok, socket}
    else
      socket =
        socket
        |> put_flash(:error, "You don't have permission to access this page.")
        |> redirect(to: ~p"/dashboard")

      {:ok, socket}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Role Management")
  end

  defp apply_action(socket, action, _params) do
    socket
    |> assign(:page_title, "Role Management - #{String.capitalize(to_string(action))}")
  end

  @impl true
  def handle_event("show_create_form", _params, socket) do
    socket = assign(socket, :show_create_form, true)
    {:noreply, socket}
  end

  def handle_event("hide_create_form", _params, socket) do
    socket = assign(socket, :show_create_form, false)
    {:noreply, socket}
  end

  def handle_event("create_role", %{"role" => role_params}, socket) do
    case UserManagement.create_role(role_params, socket.assigns.current_user.id) do
      {:ok, _role} ->
        roles = UserManagement.list_roles()
        socket =
          socket
          |> assign(:roles, roles)
          |> assign(:show_create_form, false)
          |> put_flash(:info, "Role created successfully")
        {:noreply, socket}

      {:error, changeset} ->
        errors = changeset_errors_to_map(changeset)
        socket =
          socket
          |> assign(:form_errors, errors)
          |> put_flash(:error, "Failed to create role")
        {:noreply, socket}
    end
  end

  def handle_event("show_assign_form", %{"role_id" => role_id}, socket) do
    role = UserManagement.get_role!(role_id)
    socket =
      socket
      |> assign(:show_assign_form, true)
      |> assign(:selected_role, role)
    {:noreply, socket}
  end

  def handle_event("hide_assign_form", _params, socket) do
    socket =
      socket
      |> assign(:show_assign_form, false)
      |> assign(:selected_role, nil)
    {:noreply, socket}
  end

  def handle_event("assign_role", %{"user_id" => user_id}, socket) do
    role = socket.assigns.selected_role
    case UserManagement.assign_role_to_user(user_id, role.id, socket.assigns.current_user.id) do
      {:ok, _assignment} ->
        users = UserManagement.list_users()
        socket =
          socket
          |> assign(:users, users)
          |> assign(:show_assign_form, false)
          |> assign(:selected_role, nil)
          |> put_flash(:info, "Role assigned successfully")
        {:noreply, socket}

      {:error, _changeset} ->
        socket = put_flash(socket, :error, "Failed to assign role")
        {:noreply, socket}
    end
  end

  def handle_event("remove_role", %{"user_id" => user_id, "role_id" => role_id}, socket) do
    case UserManagement.remove_role_from_user(user_id, role_id, socket.assigns.current_user.id) do
      {:ok, _assignment} ->
        users = UserManagement.list_users()
        socket =
          socket
          |> assign(:users, users)
          |> put_flash(:info, "Role removed successfully")
        {:noreply, socket}

      {:error, _} ->
        socket = put_flash(socket, :error, "Failed to remove role")
        {:noreply, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Role Management</h1>
          <p class="text-gray-600">Manage user roles and permissions</p>
        </div>
        <button
          phx-click="show_create_form"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Create New Role
        </button>
      </div>

      <!-- Create Role Form -->
      <%= if @show_create_form do %>
        <div class="bg-white p-6 rounded-lg shadow-sm border">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Create New Role</h3>
            <button phx-click="hide_create_form" class="text-gray-400 hover:text-gray-600">
              <.icon name="hero-x-mark" class="w-5 h-5" />
            </button>
          </div>

          <form phx-submit="create_role" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
              <input
                type="text"
                name="role[name]"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter role name"
              />
              <%= if Map.get(@form_errors, :name) do %>
                <p class="text-red-600 text-sm mt-1"><%= @form_errors.name %></p>
              <% end %>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                name="role[description]"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter role description"
              ></textarea>
              <%= if Map.get(@form_errors, :description) do %>
                <p class="text-red-600 text-sm mt-1"><%= @form_errors.description %></p>
              <% end %>
            </div>

            <div class="flex space-x-3">
              <button
                type="submit"
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Create Role
              </button>
              <button
                type="button"
                phx-click="hide_create_form"
                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      <% end %>

      <!-- Roles List -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">System Roles</h3>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= for role <- @roles do %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900"><%= role.name %></div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900"><%= role.description %></div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <%= if role.is_system_role do %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        System
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Custom
                      </span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      <%= count_users_with_role(@users, role.id) %> users
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      phx-click="show_assign_form"
                      phx-value-role_id={role.id}
                      class="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      Assign Users
                    </button>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Role Assignment Form -->
      <%= if @show_assign_form && @selected_role do %>
        <div class="bg-white p-6 rounded-lg shadow-sm border">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              Assign "<%= @selected_role.name %>" Role to Users
            </h3>
            <button phx-click="hide_assign_form" class="text-gray-400 hover:text-gray-600">
              <.icon name="hero-x-mark" class="w-5 h-5" />
            </button>
          </div>

          <div class="space-y-4">
            <%= for user <- @users do %>
              <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <span class="text-sm font-medium text-gray-700">
                        <%= get_user_initials(user) %>
                      </span>
                    </div>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">
                      <%= get_user_display_name(user) %>
                    </div>
                    <div class="text-sm text-gray-500"><%= user.email %></div>
                  </div>
                </div>

                <div class="flex items-center space-x-2">
                  <%= if user_has_role?(user, @selected_role.id) do %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Has Role
                    </span>
                    <button
                      phx-click="remove_role"
                      phx-value-user_id={user.id}
                      phx-value-role_id={@selected_role.id}
                      class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      Remove
                    </button>
                  <% else %>
                    <button
                      phx-click="assign_role"
                      phx-value-user_id={user.id}
                      class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Assign Role
                    </button>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- User Roles Overview -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">User Role Assignments</h3>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= for user <- @users do %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 w-8 h-8">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                          <span class="text-sm font-medium text-gray-700">
                            <%= get_user_initials(user) %>
                          </span>
                        </div>
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">
                          <%= get_user_display_name(user) %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900"><%= user.email %></div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="flex flex-wrap gap-1">
                      <%= for role <- get_user_roles(user) do %>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <%= role.name %>
                        </span>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <%= case user.status do %>
                      <% "active" -> %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      <% "inactive" -> %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Inactive
                        </span>
                      <% "suspended" -> %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Suspended
                        </span>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    """
  end

  defp can_manage_roles?(user) do
    UserManagement.user_has_role?(user, "admin") ||
    UserManagement.user_has_permission?(user, "roles", "read")
  end

  defp changeset_errors_to_map(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
  end

  defp count_users_with_role(users, role_id) do
    users
    |> Enum.count(fn user -> user_has_role?(user, role_id) end)
  end

  defp user_has_role?(user, role_id) do
    case user.role_assignments do
      %Ecto.Association.NotLoaded{} -> false
      role_assignments ->
        Enum.any?(role_assignments, fn assignment ->
          assignment.role_id == role_id
        end)
    end
  end

  defp get_user_roles(user) do
    case user.role_assignments do
      %Ecto.Association.NotLoaded{} -> []
      role_assignments ->
        Enum.map(role_assignments, fn assignment -> assignment.role end)
    end
  end

  defp get_user_initials(user) do
    case user.profile do
      %Ecto.Association.NotLoaded{} ->
        user.email |> String.first() |> String.upcase()
      nil ->
        user.email |> String.first() |> String.upcase()
      profile ->
        first = if profile.first_name, do: String.first(profile.first_name), else: ""
        last = if profile.last_name, do: String.first(profile.last_name), else: ""
        case {first, last} do
          {"", ""} -> user.email |> String.first() |> String.upcase()
          {f, ""} -> String.upcase(f)
          {"", l} -> String.upcase(l)
          {f, l} -> String.upcase(f <> l)
        end
    end
  end

  defp get_user_display_name(user) do
    case user.profile do
      %Ecto.Association.NotLoaded{} -> user.email
      nil -> user.email
      profile ->
        case {profile.first_name, profile.last_name} do
          {nil, nil} -> user.email
          {first, nil} -> first
          {nil, last} -> last
          {first, last} -> "#{first} #{last}"
        end
    end
  end
end
