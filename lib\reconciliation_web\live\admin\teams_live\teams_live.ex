defmodule ReconciliationWeb.Admin.TeamsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.UserManagement
  alias Reconciliation.Organizations.{Organization, Team}
  alias Reconciliation.Accounts.UserTeamMembership

  @impl true
  def mount(_params, _session, socket) do
    if can_manage_teams?(socket.assigns.current_user) do
      organizations = UserManagement.list_organizations()
      teams = if get_default_organization_id(organizations) do
        UserManagement.list_teams(get_default_organization_id(organizations))
      else
        []
      end
      users = UserManagement.list_users()

      socket =
        socket
        |> assign(:page_title, "Team Management")
        |> assign(:organizations, organizations)
        |> assign(:teams, teams)
        |> assign(:users, users)
        |> assign(:selected_organization_id, get_default_organization_id(organizations))
        |> assign(:search_term, "")
        |> assign(:show_form, false)
        |> assign(:show_member_form, false)
        |> assign(:editing_team, nil)
        |> assign(:selected_team, nil)
        |> assign(:form, nil)
        |> assign(:member_form, nil)

      {:ok, socket}
    else
      socket =
        socket
        |> put_flash(:error, "You don't have permission to access this page.")
        |> redirect(to: ~p"/dashboard")

      {:ok, socket}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Team Management")
  end

  @impl true
  def handle_event("change_organization", %{"organization_id" => org_id}, socket) do
    organization_id = if org_id == "all", do: nil, else: String.to_integer(org_id)
    teams = if organization_id, do: UserManagement.list_teams(organization_id), else: []

    {:noreply,
     socket
     |> assign(:selected_organization_id, organization_id)
     |> assign(:teams, teams)
    }
  end

  def handle_event("search", %{"search" => term}, socket) do
    {:noreply, assign(socket, :search_term, term)}
  end

  def handle_event("new_team", _params, socket) do
    changeset = Team.changeset(%Team{}, %{})
    form = to_form(changeset)

    {:noreply,
     socket
     |> assign(:show_form, true)
     |> assign(:editing_team, nil)
     |> assign(:form, form)
    }
  end

  def handle_event("edit_team", %{"id" => id}, socket) do
    team = Enum.find(socket.assigns.teams, &(&1.id == String.to_integer(id)))
    changeset = Team.changeset(team, %{})
    form = to_form(changeset)

    {:noreply,
     socket
     |> assign(:show_form, true)
     |> assign(:editing_team, team)
     |> assign(:form, form)
    }
  end

  def handle_event("cancel_form", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_form, false)
     |> assign(:show_member_form, false)
     |> assign(:editing_team, nil)
     |> assign(:selected_team, nil)
     |> assign(:form, nil)
     |> assign(:member_form, nil)
    }
  end

  def handle_event("validate", %{"team" => team_params}, socket) do
    team = socket.assigns.editing_team || %Team{}
    changeset = Team.changeset(team, team_params)
    form = to_form(changeset, action: :validate)

    {:noreply, assign(socket, :form, form)}
  end

  def handle_event("save", %{"team" => team_params}, socket) do
    case socket.assigns.editing_team do
      nil ->
        # Create new team
        team_params = Map.put(team_params, "organization_id", socket.assigns.selected_organization_id)
        case UserManagement.create_team(team_params, socket.assigns.current_user.id) do
          {:ok, _team} ->
            teams = UserManagement.list_teams(socket.assigns.selected_organization_id)
            {:noreply,
             socket
             |> assign(:teams, teams)
             |> assign(:show_form, false)
             |> assign(:form, nil)
             |> put_flash(:info, "Team created successfully.")
            }
          {:error, changeset} ->
            form = to_form(changeset)
            {:noreply, assign(socket, :form, form)}
        end

      team ->
        # Update existing team
        case UserManagement.update_team(team, team_params, socket.assigns.current_user.id) do
          {:ok, _team} ->
            teams = UserManagement.list_teams(socket.assigns.selected_organization_id)
            {:noreply,
             socket
             |> assign(:teams, teams)
             |> assign(:show_form, false)
             |> assign(:editing_team, nil)
             |> assign(:form, nil)
             |> put_flash(:info, "Team updated successfully.")
            }
          {:error, changeset} ->
            form = to_form(changeset)
            {:noreply, assign(socket, :form, form)}
        end
    end
  end

  def handle_event("delete_team", %{"id" => id}, socket) do
    team = Enum.find(socket.assigns.teams, &(&1.id == String.to_integer(id)))

    case UserManagement.delete_team(team, socket.assigns.current_user.id) do
      {:ok, _} ->
        teams = UserManagement.list_teams(socket.assigns.selected_organization_id)
        {:noreply,
         socket
         |> assign(:teams, teams)
         |> put_flash(:info, "Team deleted successfully.")
        }
      {:error, _} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete team.")
        }
    end
  end

  def handle_event("manage_members", %{"id" => id}, socket) do
    team = Enum.find(socket.assigns.teams, &(&1.id == String.to_integer(id)))
    team_with_users = UserManagement.get_team_with_users!(team.id)

    # Create form for adding new members
    changeset = UserTeamMembership.changeset(%UserTeamMembership{}, %{})
    form = to_form(changeset)

    {:noreply,
     socket
     |> assign(:show_member_form, true)
     |> assign(:selected_team, team_with_users)
     |> assign(:member_form, form)
    }
  end

  def handle_event("validate_member", %{"user_team_membership" => member_params}, socket) do
    changeset = UserTeamMembership.changeset(%UserTeamMembership{}, member_params)
    form = to_form(changeset, action: :validate)

    {:noreply, assign(socket, :member_form, form)}
  end

  def handle_event("add_member", %{"user_team_membership" => member_params}, socket) do
    team = socket.assigns.selected_team
    user_id = String.to_integer(member_params["user_id"])
    role = member_params["role"]

    case UserManagement.add_user_to_team(user_id, team.id, role, socket.assigns.current_user.id) do
      {:ok, _membership} ->
        # Refresh team data
        updated_team = UserManagement.get_team_with_users!(team.id)
        teams = UserManagement.list_teams(socket.assigns.selected_organization_id)

        changeset = UserTeamMembership.changeset(%UserTeamMembership{}, %{})
        form = to_form(changeset)

        {:noreply,
         socket
         |> assign(:teams, teams)
         |> assign(:selected_team, updated_team)
         |> assign(:member_form, form)
         |> put_flash(:info, "Member added successfully.")
        }
      {:error, changeset} ->
        form = to_form(changeset)
        {:noreply, assign(socket, :member_form, form)}
    end
  end

  def handle_event("remove_member", %{"user_id" => user_id}, socket) do
    team = socket.assigns.selected_team
    user_id = String.to_integer(user_id)

    case UserManagement.remove_user_from_team(user_id, team.id, socket.assigns.current_user.id) do
      {:ok, _} ->
        # Refresh team data
        updated_team = UserManagement.get_team_with_users!(team.id)
        teams = UserManagement.list_teams(socket.assigns.selected_organization_id)

        {:noreply,
         socket
         |> assign(:teams, teams)
         |> assign(:selected_team, updated_team)
         |> put_flash(:info, "Member removed successfully.")
        }
      {:error, _} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to remove member.")
        }
    end
  end

  def handle_event("update_member_role", %{"user_id" => user_id, "role" => new_role}, socket) do
    team = socket.assigns.selected_team
    user_id = String.to_integer(user_id)

    case UserManagement.update_team_member_role(user_id, team.id, new_role, socket.assigns.current_user.id) do
      {:ok, _} ->
        # Refresh team data
        updated_team = UserManagement.get_team_with_users!(team.id)
        teams = UserManagement.list_teams(socket.assigns.selected_organization_id)

        {:noreply,
         socket
         |> assign(:teams, teams)
         |> assign(:selected_team, updated_team)
         |> put_flash(:info, "Member role updated successfully.")
        }
      {:error, _} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update member role.")
        }
    end
  end

  defp apply_action(socket, action, _params) do
    socket
    |> assign(:page_title, "Team Management - #{String.capitalize(to_string(action))}")
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Team Management</h1>
          <p class="text-gray-600">Manage teams and team memberships</p>
        </div>
        <button
          :if={@selected_organization_id}
          phx-click="new_team"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium"
        >
          New Team
        </button>
      </div>

      <!-- Organization and Search Controls -->
      <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="sm:w-64">
            <label class="block text-sm font-medium text-gray-700 mb-1">Organization</label>
            <form phx-change="change_organization">
              <select
                name="organization_id"
                class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">Select Organization</option>
                <%= for org <- @organizations do %>
                  <option value={org.id} selected={@selected_organization_id == org.id}>
                    <%= org.name %>
                  </option>
                <% end %>
              </select>
            </form>
          </div>
          <div class="flex-1">
            <label class="block text-sm font-medium text-gray-700 mb-1">Search Teams</label>
            <form phx-change="search">
              <input
                type="text"
                name="search"
                value={@search_term}
                placeholder="Search teams..."
                class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </form>
          </div>
        </div>
      </div>

      <!-- Teams List -->
      <%= if @selected_organization_id do %>
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Teams</h3>
          </div>

          <%= if Enum.empty?(filtered_teams(assigns)) do %>
            <div class="p-8 text-center">
              <div class="text-gray-500 mb-4">
                <.icon name="hero-user-group" class="w-12 h-12 mx-auto" />
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No teams found</h3>
              <p class="text-gray-600">Get started by creating your first team for this organization.</p>
            </div>
          <% else %>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Team
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Members
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <%= for team <- filtered_teams(assigns) do %>
                    <tr class="hover:bg-gray-50">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div class="text-sm font-medium text-gray-900"><%= team.name %></div>
                          <%= if team.description do %>
                            <div class="text-sm text-gray-600 mt-1"><%= team.description %></div>
                          <% end %>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                          <%= Map.get(team, :member_count, 0) %> members
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= format_date(team.inserted_at) %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                          <button
                            phx-click="manage_members"
                            phx-value-id={team.id}
                            class="text-green-600 hover:text-green-900"
                          >
                            Members
                          </button>
                          <button
                            phx-click="edit_team"
                            phx-value-id={team.id}
                            class="text-blue-600 hover:text-blue-900"
                          >
                            Edit
                          </button>
                          <button
                            phx-click="delete_team"
                            phx-value-id={team.id}
                            data-confirm="Are you sure you want to delete this team?"
                            class="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="bg-white p-8 rounded-lg shadow-sm border text-center">
          <div class="text-gray-500 mb-4">
            <.icon name="hero-building-office" class="w-12 h-12 mx-auto" />
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Select an Organization</h3>
          <p class="text-gray-600">Please select an organization to view and manage its teams.</p>
        </div>
      <% end %>

      <!-- Team Form Modal -->
      <%= if @show_form do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                <%= if @editing_team, do: "Edit Team", else: "New Team" %>
              </h3>

              <.form for={@form} phx-change="validate" phx-submit="save" class="space-y-4">
                <div>
                  <.input field={@form[:name]} type="text" label="Team Name" required />
                </div>

                <div>
                  <.input field={@form[:description]} type="textarea" label="Description" rows="3" />
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    phx-click="cancel_form"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
                  >
                    <%= if @editing_team, do: "Update", else: "Create" %>
                  </button>
                </div>
              </.form>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Team Members Modal -->
      <%= if @show_member_form && @selected_team do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900">
                  Manage Team Members - <%= @selected_team.name %>
                </h3>
                <button
                  phx-click="cancel_form"
                  class="text-gray-400 hover:text-gray-600"
                >
                  <.icon name="hero-x-mark" class="w-6 h-6" />
                </button>
              </div>

              <!-- Add New Member Form -->
              <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-md font-medium text-gray-900 mb-3">Add New Member</h4>
                <.form for={@member_form} phx-change="validate_member" phx-submit="add_member" class="flex gap-4">
                  <div class="flex-1">
                    <.input
                      field={@member_form[:user_id]}
                      type="select"
                      label="User"
                      options={available_users_options(@users, @selected_team)}
                      prompt="Select a user"
                    />
                  </div>
                  <div class="w-32">
                    <.input
                      field={@member_form[:role]}
                      type="select"
                      label="Role"
                      options={[{"Member", "member"}, {"Lead", "lead"}]}
                    />
                  </div>
                  <div class="flex items-end">
                    <button
                      type="submit"
                      class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md"
                    >
                      Add
                    </button>
                  </div>
                </.form>
              </div>

              <!-- Current Members List -->
              <div>
                <h4 class="text-md font-medium text-gray-900 mb-3">Current Members</h4>
                <%= if Enum.empty?(@selected_team.user_team_memberships) do %>
                  <p class="text-gray-500 text-center py-4">No members in this team yet.</p>
                <% else %>
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User
                          </th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Role
                          </th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Joined
                          </th>
                          <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <%= for membership <- @selected_team.user_team_memberships do %>
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">
                                <%= membership.user.email %>
                              </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <form phx-change="update_member_role" phx-value-user_id={membership.user.id}>
                                <select
                                  name="role"
                                  class="text-sm border border-gray-300 rounded px-2 py-1"
                                >
                                  <option value="member" selected={membership.role == "member"}>Member</option>
                                  <option value="lead" selected={membership.role == "lead"}>Lead</option>
                                </select>
                              </form>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <%= format_date(membership.inserted_at) %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                phx-click="remove_member"
                                phx-value-user_id={membership.user.id}
                                data-confirm="Are you sure you want to remove this member?"
                                class="text-red-600 hover:text-red-900"
                              >
                                Remove
                              </button>
                            </td>
                          </tr>
                        <% end %>
                      </tbody>
                    </table>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper functions
  defp get_default_organization_id([]), do: nil
  defp get_default_organization_id([org | _]), do: org.id

  defp filtered_teams(assigns) do
    teams = assigns.teams
    search_term = String.downcase(assigns.search_term || "")

    teams
    |> filter_teams_by_search(search_term)
  end

  defp filter_teams_by_search(teams, ""), do: teams
  defp filter_teams_by_search(teams, search_term) do
    Enum.filter(teams, fn team ->
      String.contains?(String.downcase(team.name), search_term) ||
      String.contains?(String.downcase(team.description || ""), search_term)
    end)
  end

  defp available_users_options(users, team) do
    current_member_ids = Enum.map(team.user_team_memberships, & &1.user.id)

    users
    |> Enum.reject(&(&1.id in current_member_ids))
    |> Enum.map(&{&1.email, &1.id})
  end

  defp format_date(nil), do: "-"
  defp format_date(date), do: Calendar.strftime(date, "%b %d, %Y")

  defp can_manage_teams?(user) do
    UserManagement.user_has_role?(user, "admin") || 
    UserManagement.user_has_role?(user, "manager") ||
    UserManagement.user_has_permission?(user, "teams", "read")
  end
end
