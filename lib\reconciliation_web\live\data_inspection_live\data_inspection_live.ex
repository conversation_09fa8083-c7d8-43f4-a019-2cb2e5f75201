defmodule ReconciliationWeb.DataInspectionLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.{Transaction, Repo}
  import Ecto.Query

  @impl true
  def mount(%{"run_id" => run_id}, _session, socket) do
    # Get raw transaction data from database
    file_a_transactions = get_raw_transactions(run_id, "file_a")
    file_b_transactions = get_raw_transactions(run_id, "file_b")

    {:ok,
     socket
     |> assign(:page_title, "Data Inspection")
     |> assign(:run_id, run_id)
     |> assign(:file_a_transactions, file_a_transactions)
     |> assign(:file_b_transactions, file_b_transactions)
    }
  end

  defp get_raw_transactions(run_id, file_type) do
    from(t in Transaction,
      join: uf in assoc(t, :uploaded_file),
      where: t.reconciliation_run_id == ^run_id and uf.file_type == ^file_type,
      order_by: t.row_number,
      limit: 10,
      select: %{
        id: t.id,
        row_number: t.row_number,
        transaction_date: t.transaction_date,
        transaction_id: t.transaction_id,
        reference: t.reference,
        description: t.description,
        amount: t.amount,
        transaction_type: t.transaction_type,
        account: t.account,
        category: t.category,
        currency: t.currency,
        is_matched: t.is_matched,
        raw_data: t.raw_data
      }
    )
    |> Repo.all()
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-7xl mx-auto p-6">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Data Inspection</h1>
        <p class="text-gray-600">Raw database data for reconciliation run <%= @run_id %></p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- File A Data -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">File A Transactions (First 10)</h2>

          <%= if Enum.any?(@file_a_transactions) do %>
            <div class="space-y-4">
              <%= for txn <- @file_a_transactions do %>
                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="grid grid-cols-2 gap-2 text-sm">
                    <div><strong>ID:</strong> <%= txn.id %></div>
                    <div><strong>Row:</strong> <%= txn.row_number %></div>

                    <div>
                      <strong>Date:</strong>
                      <%= if txn.transaction_date do %>
                        <%= Date.to_string(txn.transaction_date) %>
                      <% else %>
                        <span class="text-red-600">nil</span>
                      <% end %>
                    </div>

                    <div>
                      <strong>Amount:</strong>
                      <%= if txn.amount do %>
                        <%= Decimal.to_string(txn.amount) %>
                      <% else %>
                        <span class="text-red-600">nil</span>
                      <% end %>
                    </div>

                    <div>
                      <strong>Reference:</strong>
                      <%= if txn.reference do %>
                        <%= txn.reference %>
                      <% else %>
                        <span class="text-red-600">nil</span>
                      <% end %>
                    </div>

                    <div>
                      <strong>Description:</strong>
                      <%= if txn.description do %>
                        <%= String.slice(txn.description, 0, 30) %><%= if String.length(txn.description) > 30, do: "..." %>
                      <% else %>
                        <span class="text-red-600">nil</span>
                      <% end %>
                    </div>

                    <div>
                      <strong>Type:</strong>
                      <%= txn.transaction_type || "nil" %>
                    </div>

                    <div>
                      <strong>Transaction ID:</strong>
                      <%= txn.transaction_id || "nil" %>
                    </div>

                    <div>
                      <strong>Account:</strong>
                      <%= txn.account || "nil" %>
                    </div>

                    <div>
                      <strong>Matched:</strong>
                      <span class={if txn.is_matched, do: "text-green-600", else: "text-red-600"}>
                        <%= txn.is_matched %>
                      </span>
                    </div>
                  </div>

                  <!-- Raw Data -->
                  <%= if txn.raw_data do %>
                    <details class="mt-2">
                      <summary class="text-xs text-gray-500 cursor-pointer">Raw Data</summary>
                      <pre class="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto"><%= inspect(txn.raw_data, pretty: true) %></pre>
                    </details>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-gray-500">No transactions found in File A</p>
          <% end %>
        </div>

        <!-- File B Data -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">File B Transactions (First 10)</h2>

          <%= if Enum.any?(@file_b_transactions) do %>
            <div class="space-y-4">
              <%= for txn <- @file_b_transactions do %>
                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="grid grid-cols-2 gap-2 text-sm">
                    <div><strong>ID:</strong> <%= txn.id %></div>
                    <div><strong>Row:</strong> <%= txn.row_number %></div>

                    <div>
                      <strong>Date:</strong>
                      <%= if txn.transaction_date do %>
                        <%= Date.to_string(txn.transaction_date) %>
                      <% else %>
                        <span class="text-red-600">nil</span>
                      <% end %>
                    </div>

                    <div>
                      <strong>Amount:</strong>
                      <%= if txn.amount do %>
                        <%= Decimal.to_string(txn.amount) %>
                      <% else %>
                        <span class="text-red-600">nil</span>
                      <% end %>
                    </div>

                    <div>
                      <strong>Reference:</strong>
                      <%= if txn.reference do %>
                        <%= txn.reference %>
                      <% else %>
                        <span class="text-red-600">nil</span>
                      <% end %>
                    </div>

                    <div>
                      <strong>Description:</strong>
                      <%= if txn.description do %>
                        <%= String.slice(txn.description, 0, 30) %><%= if String.length(txn.description) > 30, do: "..." %>
                      <% else %>
                        <span class="text-red-600">nil</span>
                      <% end %>
                    </div>

                    <div>
                      <strong>Type:</strong>
                      <%= txn.transaction_type || "nil" %>
                    </div>

                    <div>
                      <strong>Transaction ID:</strong>
                      <%= txn.transaction_id || "nil" %>
                    </div>

                    <div>
                      <strong>Account:</strong>
                      <%= txn.account || "nil" %>
                    </div>

                    <div>
                      <strong>Matched:</strong>
                      <span class={if txn.is_matched, do: "text-green-600", else: "text-red-600"}>
                        <%= txn.is_matched %>
                      </span>
                    </div>
                  </div>

                  <!-- Raw Data -->
                  <%= if txn.raw_data do %>
                    <details class="mt-2">
                      <summary class="text-xs text-gray-500 cursor-pointer">Raw Data</summary>
                      <pre class="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto"><%= inspect(txn.raw_data, pretty: true) %></pre>
                    </details>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-gray-500">No transactions found in File B</p>
          <% end %>
        </div>
      </div>

      <!-- Comparison Summary -->
      <div class="bg-white rounded-lg shadow-sm border p-6 mt-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Comparison</h2>

        <%= if Enum.any?(@file_a_transactions) and Enum.any?(@file_b_transactions) do %>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 class="font-medium text-gray-900 mb-2">First Transaction Comparison</h3>
              <% first_a = List.first(@file_a_transactions) %>
              <% first_b = List.first(@file_b_transactions) %>
              <div class="text-sm space-y-1">
                <div>
                  <strong>Amounts Equal:</strong>
                  <span class={if Decimal.equal?(first_a.amount, first_b.amount), do: "text-green-600", else: "text-red-600"}>
                    <%= Decimal.equal?(first_a.amount, first_b.amount) %>
                  </span>
                </div>
                <div>
                  <strong>Dates Equal:</strong>
                  <span class={if first_a.transaction_date == first_b.transaction_date, do: "text-green-600", else: "text-red-600"}>
                    <%= first_a.transaction_date == first_b.transaction_date %>
                  </span>
                </div>
                <div>
                  <strong>References Equal:</strong>
                  <span class={if first_a.reference == first_b.reference, do: "text-green-600", else: "text-red-600"}>
                    <%= first_a.reference == first_b.reference %>
                  </span>
                </div>
                <div>
                  <strong>Descriptions Equal:</strong>
                  <span class={if first_a.description == first_b.description, do: "text-green-600", else: "text-red-600"}>
                    <%= first_a.description == first_b.description %>
                  </span>
                </div>
                <div>
                  <strong>Transaction Types Equal:</strong>
                  <span class={if first_a.transaction_type == first_b.transaction_type, do: "text-green-600", else: "text-red-600"}>
                    <%= first_a.transaction_type == first_b.transaction_type %>
                  </span>
                </div>
                <div>
                  <strong>Transaction IDs Equal:</strong>
                  <span class={if first_a.transaction_id == first_b.transaction_id, do: "text-green-600", else: "text-red-600"}>
                    <%= first_a.transaction_id == first_b.transaction_id %>
                  </span>
                </div>
                <div>
                  <strong>Accounts Equal:</strong>
                  <span class={if first_a.account == first_b.account, do: "text-green-600", else: "text-red-600"}>
                    <%= first_a.account == first_b.account %>
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 class="font-medium text-gray-900 mb-2">Data Quality Check</h3>
              <div class="text-sm space-y-1">
                <div>
                  <strong>File A nil dates:</strong>
                  <%= Enum.count(@file_a_transactions, &is_nil(&1.transaction_date)) %>/<%= length(@file_a_transactions) %>
                </div>
                <div>
                  <strong>File B nil dates:</strong>
                  <%= Enum.count(@file_b_transactions, &is_nil(&1.transaction_date)) %>/<%= length(@file_b_transactions) %>
                </div>
                <div>
                  <strong>File A nil references:</strong>
                  <%= Enum.count(@file_a_transactions, &is_nil(&1.reference)) %>/<%= length(@file_a_transactions) %>
                </div>
                <div>
                  <strong>File B nil references:</strong>
                  <%= Enum.count(@file_b_transactions, &is_nil(&1.reference)) %>/<%= length(@file_b_transactions) %>
                </div>
                <div>
                  <strong>File A nil transaction types:</strong>
                  <%= Enum.count(@file_a_transactions, &is_nil(&1.transaction_type)) %>/<%= length(@file_a_transactions) %>
                </div>
                <div>
                  <strong>File B nil transaction types:</strong>
                  <%= Enum.count(@file_b_transactions, &is_nil(&1.transaction_type)) %>/<%= length(@file_b_transactions) %>
                </div>
                <div>
                  <strong>File A nil transaction IDs:</strong>
                  <%= Enum.count(@file_a_transactions, &is_nil(&1.transaction_id)) %>/<%= length(@file_a_transactions) %>
                </div>
                <div>
                  <strong>File B nil transaction IDs:</strong>
                  <%= Enum.count(@file_b_transactions, &is_nil(&1.transaction_id)) %>/<%= length(@file_b_transactions) %>
                </div>
                <div>
                  <strong>File A nil accounts:</strong>
                  <%= Enum.count(@file_a_transactions, &is_nil(&1.account)) %>/<%= length(@file_a_transactions) %>
                </div>
                <div>
                  <strong>File B nil accounts:</strong>
                  <%= Enum.count(@file_b_transactions, &is_nil(&1.account)) %>/<%= length(@file_b_transactions) %>
                </div>
              </div>
            </div>

            <div>
              <h3 class="font-medium text-gray-900 mb-2">Match Status</h3>
              <div class="text-sm space-y-1">
                <div>
                  <strong>File A matched:</strong>
                  <%= Enum.count(@file_a_transactions, &(&1.is_matched)) %>/<%= length(@file_a_transactions) %>
                </div>
                <div>
                  <strong>File B matched:</strong>
                  <%= Enum.count(@file_b_transactions, &(&1.is_matched)) %>/<%= length(@file_b_transactions) %>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <p class="text-gray-500">Not enough data for comparison</p>
        <% end %>
      </div>
    </div>
    """
  end
end
