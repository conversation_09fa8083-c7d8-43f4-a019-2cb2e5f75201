defmodule ReconciliationWeb.ReconciliationUploadLive do
  use ReconciliationWeb, :live_view
  import Phoenix.Component

  require Logger # Added this line

  alias Reconciliation.Services.{ExcelParser, MatchingEngine, FileProcessingService}

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user
    today = DateTime.utc_now() |> DateTime.to_date()

    {:ok,
     socket
     |> assign(:page_title, "Upload Reconciliation Files")
     |> assign(:current_user, user)
     |> assign(:reconciliation_run, nil)  # No run created yet
     |> assign(:file_a_uploaded, nil)
     |> assign(:file_b_uploaded, nil)
     |> assign(:file_a_client_upload_complete, false)
     |> assign(:file_b_client_upload_complete, false)
     |> assign(:processing_status, %{file_a: nil, file_b: nil})
     |> assign(:upload_status, %{})
     |> assign(:database_status, %{})
     |> assign(:show_confirmation, false)  # Add confirmation dialog state
     |> assign(:form, to_form(%{"name" => generate_default_run_name()}))
     |> allow_upload(:file_a,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 50_000_000, # 50MB
          auto_upload: true,
          progress: &handle_progress/3
        )
     |> allow_upload(:file_b,
          accept: ~w(.xlsx .xls .csv),
          max_entries: 1,
          max_file_size: 50_000_000, # 50MB
          auto_upload: true,
          progress: &handle_progress/3
        )
    }
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("show_confirmation", _params, socket) do
    {:noreply, assign(socket, :show_confirmation, true)}
  end

  @impl true
  def handle_event("cancel_confirmation", _params, socket) do
    {:noreply, assign(socket, :show_confirmation, false)}
  end

  @impl true
  def handle_event("confirm_upload", _params, socket) do
    # Hide confirmation dialog and proceed with upload
    socket = assign(socket, :show_confirmation, false)

    # Call the original save logic
    updated_socket = handle_save_logic(socket)

    # Always redirect to transactions page immediately after starting upload
    updated_socket =
      updated_socket
      |> put_flash(:info, "✅ New files uploaded successfully! A new reconciliation run is ready for processing.")
      |> push_event("show_toast", %{
        type: "success",
        title: "Files Uploaded!",
        message: "New reconciliation run created and ready for processing",
        duration: 5000
      })

    {:noreply, push_navigate(updated_socket, to: ~p"/transactions")}
  end

  @impl true
  def handle_event("stop_propagation", _params, socket) do
    # This event handler prevents the modal from closing when clicking inside it
    {:noreply, socket}
  end

  def handle_progress(:file_a, entry, socket) do
    require Logger
    Logger.info("FILE_A_PROGRESS: #{entry.progress}% (#{entry.ref})")
    IO.inspect(entry, label: "File A Progress Entry")
    socket = handle_progress_update(entry, socket)
    # Check for client-side validation errors on the specific upload config
    upload_config_has_errors = !Enum.empty?(socket.assigns.uploads.file_a.errors)

    socket =
      if entry.progress == 100 && !upload_config_has_errors do
        assign(socket, :file_a_client_upload_complete, true)
      else
        # If progress is not 100 OR there are errors, it's not complete for button purposes
        assign(socket, :file_a_client_upload_complete, false)
      end
    {:noreply, socket}
  end

  def handle_progress(:file_b, entry, socket) do
    require Logger
    Logger.info("FILE_B_PROGRESS: #{entry.progress}% (#{entry.ref})")
    IO.inspect(entry, label: "File B Progress Entry")
    socket = handle_progress_update(entry, socket)
    # Check for client-side validation errors on the specific upload config
    upload_config_has_errors = !Enum.empty?(socket.assigns.uploads.file_b.errors)

    socket =
      if entry.progress == 100 && !upload_config_has_errors do
        assign(socket, :file_b_client_upload_complete, true)
      else
        # If progress is not 100 OR there are errors, it's not complete for button purposes
        assign(socket, :file_b_client_upload_complete, false)
      end
    {:noreply, socket}
  end

  @impl true
  def handle_event("update_name", %{"name" => name}, socket) do
    # If run exists, update it; otherwise just update the form
    socket = case socket.assigns.reconciliation_run do
      nil ->
        # No run created yet, just update the form
        assign(socket, :form, to_form(%{"name" => name}))

      run ->
        # Run exists, update it
        {:ok, updated_run} = Reconciliation.update_reconciliation_run(run, %{name: name})
        socket
        |> assign(:reconciliation_run, updated_run)
        |> assign(:form, to_form(%{"name" => name}))
    end

    {:noreply, socket}
  end

  @impl true
  def handle_event("save", _params, socket) do
    # Show confirmation dialog instead of directly uploading
    {:noreply, assign(socket, :show_confirmation, true)}
  end

  # Extract the actual save logic into a separate function
  defp handle_save_logic(socket) do
    # Create reconciliation run now that user is actually uploading
    run = case socket.assigns.reconciliation_run do
      nil ->
        # Generate smart name based on uploaded files
        run_name = generate_smart_run_name(socket)
        {:ok, new_run} = Reconciliation.create_reconciliation_run(%{
          name: run_name,
          user_id: socket.assigns.current_user.id,
          status: "pending"
        })

        # Subscribe to upload progress updates for the new run
        Phoenix.PubSub.subscribe(Reconciliation.PubSub, "upload_progress:#{new_run.id}")
        new_run

      existing_run ->
        existing_run
    end

    # Update socket with the run
    socket = assign(socket, :reconciliation_run, run)

    # Process file uploads using service
    file_a_results = consume_uploaded_entries(socket, :file_a, &handle_file_upload_with_service(&1, &2, "file_a", socket))
    file_b_results = consume_uploaded_entries(socket, :file_b, &handle_file_upload_with_service(&1, &2, "file_b", socket))

    # Flash errors from file_a upload processing
    socket =
      case file_a_results do
        [{:error, reason}] ->
          put_flash(socket, :error, "File A processing error: #{reason}")
        _ ->
          socket
      end

    # Flash errors from file_b upload processing
    socket =
      case file_b_results do
        [{:error, reason}] ->
          put_flash(socket, :error, "File B processing error: #{reason}")
        _ ->
          socket
      end

    socket =
      socket
      |> update_file_status(:file_a, file_a_results)
      |> update_file_status(:file_b, file_b_results)

    # Check if both files are uploaded
    if socket.assigns.file_a_uploaded && socket.assigns.file_b_uploaded do
      # Start processing files
      Task.start(fn -> process_reconciliation(socket.assigns.reconciliation_run.id) end)

      put_flash(socket, :info, "Files uploaded successfully! Processing reconciliation...")
    else
      socket
    end
  end

  # Handle upload cancellations
  @impl true
  def handle_event("cancel_upload", %{"ref" => ref, "type" => "file_a"}, socket) do
    socket = assign(socket, :file_a_client_upload_complete, false)
    {:noreply, cancel_upload(socket, :file_a, ref)}
  end

  def handle_event("cancel_upload", %{"ref" => ref, "type" => "file_b"}, socket) do
    socket = assign(socket, :file_b_client_upload_complete, false)
    {:noreply, cancel_upload(socket, :file_b, ref)}
  end

  def handle_event("cancel_upload", %{"ref" => ref}, socket) do
    # Fallback for old format
    socket =
      socket
      |> cancel_upload(:file_a, ref)
      |> cancel_upload(:file_b, ref)
    {:noreply, socket}
  end

  # Handle PubSub messages for database insertion progress
  @impl true
  def handle_info({:database_progress, file_id, progress}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, progress)
    {:noreply, assign(socket, :database_status, database_status)}
  end

  def handle_info({:database_complete, file_id, result}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, %{
      status: "complete",
      result: result,
      completed_at: DateTime.utc_now()
    })
    {:noreply, assign(socket, :database_status, database_status)}
  end

  def handle_info({:database_error, file_id, error}, socket) do
    database_status = Map.put(socket.assigns.database_status, file_id, %{
      status: "error",
      error: error,
      failed_at: DateTime.utc_now()
    })

    # Show user-friendly error message
    socket = put_flash(socket, :error, "Failed to process file: #{error}")

    {:noreply, assign(socket, :database_status, database_status)}
  end

  # Handle retry upload event
  @impl true
  def handle_event("retry_upload", %{"file_id" => file_id}, socket) do
    # Find the uploaded file and retry processing
    case Reconciliation.get_uploaded_file(file_id) do
      nil ->
        {:noreply, put_flash(socket, :error, "File not found")}

      uploaded_file ->
        # Ensure we have a reconciliation run
        case socket.assigns.reconciliation_run do
          nil ->
            {:noreply, put_flash(socket, :error, "No reconciliation run found")}

          run ->
            # Reset database status
            database_status = Map.delete(socket.assigns.database_status, uploaded_file.id)
            socket = assign(socket, :database_status, database_status)

            # Restart processing
            Task.start(fn ->
              FileProcessingService.process_file_with_progress(uploaded_file, run.id)
            end)

            {:noreply, put_flash(socket, :info, "Retrying file processing...")}
        end
    end
  end

  # Handle upload progress updates
  defp handle_progress_update(entry, socket) do
    upload_status = Map.get(socket.assigns.upload_status, entry.ref, %{})

    # Calculate upload speed and ETA
    current_time = System.monotonic_time(:millisecond)
    bytes_uploaded = trunc(entry.progress / 100 * entry.client_size)

    updated_status =
      case Map.get(upload_status, :start_time) do
        nil ->
          Map.merge(upload_status, %{
            start_time: current_time,
            bytes_uploaded: bytes_uploaded,
            upload_speed: 0,
            eta_seconds: nil
          })

        start_time ->
          elapsed_ms = current_time - start_time
          if elapsed_ms > 0 do
            upload_speed = bytes_uploaded / (elapsed_ms / 1000) # bytes per second
            remaining_bytes = entry.client_size - bytes_uploaded
            eta_seconds = if upload_speed > 0, do: trunc(remaining_bytes / upload_speed), else: nil

            Map.merge(upload_status, %{
              bytes_uploaded: bytes_uploaded,
              upload_speed: upload_speed,
              eta_seconds: eta_seconds
            })
          else
            upload_status
          end
      end

    assign(socket, :upload_status, Map.put(socket.assigns.upload_status, entry.ref, updated_status))
  end

  # Handle file upload using service
  defp handle_file_upload_with_service(meta, entry, file_type, socket) do
    case FileProcessingService.handle_file_upload(meta, entry, file_type, socket.assigns.reconciliation_run.id) do
      {:ok, uploaded_file} ->
        # Start database insertion process with progress tracking
        Task.start(fn ->
          FileProcessingService.process_file_with_progress(uploaded_file, socket.assigns.reconciliation_run.id)
        end)
        {:ok, uploaded_file}
      {:error, error} ->
        {:error, error}
    end
  end

  # Update file status in socket assigns
  defp update_file_status(socket, file_type, results) do
    case results do
      [{:ok, uploaded_file}] ->
        assign(socket, String.to_atom("#{file_type}_uploaded"), uploaded_file)
      _ ->
        socket
    end
  end

  # Note: File processing logic moved to FileProcessingService

  # Process reconciliation in background
  defp process_reconciliation(reconciliation_run_id) do
    Logger.info("[#{reconciliation_run_id}] Starting background reconciliation task.")

    try do
      # Attempt to fetch the run first
      case Reconciliation.get_reconciliation_run!(reconciliation_run_id) do
        nil ->
          error_message = "ReconciliationRun record not found for ID: #{reconciliation_run_id}. Cannot process."
          Logger.error("[#{reconciliation_run_id}] #{error_message}")
          # Attempt to mark as failed by ID if the run struct couldn't be fetched
          Reconciliation.mark_reconciliation_failed_by_id(reconciliation_run_id, error_message)

        %Reconciliation.ReconciliationRun{} = run ->
          Logger.info("[#{run.id}] Successfully fetched ReconciliationRun. User: #{run.user_id}")
          uploaded_files = Reconciliation.get_uploaded_files(run.id)
          Logger.info("[#{run.id}] Found #{length(uploaded_files)} uploaded files.")

          # Inner try for the main processing logic
          try do
            # Update status to processing
            case Reconciliation.update_reconciliation_run(run, %{status: "processing"}) do
              {:ok, updated_run} ->
                Logger.info("[#{updated_run.id}] Status updated to 'processing'.")
              {:error, changeset} ->
                error_msg = "Failed to update run status to 'processing': #{inspect(changeset.errors)}"
                Logger.error("[#{run.id}] #{error_msg}")
                # Mark as failed if status update fails before main work
                Reconciliation.mark_reconciliation_failed(run, error_msg)
                # The run is marked as failed. Subsequent parsing/matching for this run will not occur
                # because we are in the :error branch of the case statement.
            end

            Logger.info("[#{run.id}] Starting parsing of uploaded files.")
            Enum.each(uploaded_files, fn file ->
              Logger.info("[#{run.id}] Parsing file: #{file.original_filename} (ID: #{file.id})")
              ExcelParser.parse_file(file)
            end)
            Logger.info("[#{run.id}] Completed parsing of uploaded files.")

            Logger.info("[#{run.id}] Starting transaction matching via MatchingEngine.")
            case MatchingEngine.match_transactions(run) do
              {:ok, matches} ->
                Logger.info("[#{run.id}] MatchingEngine.match_transactions completed successfully with #{length(matches)} matches. Reconciliation run should now be marked as 'completed'.")
              {:error, error} ->
                Logger.error("[#{run.id}] MatchingEngine.match_transactions failed: #{inspect(error)}")
                Reconciliation.mark_reconciliation_failed(run, "Matching failed: #{inspect(error)}")
            end

          rescue
            inner_error ->
              error_message = "Error during reconciliation processing: #{Exception.message(inner_error)}\nStacktrace: #{inspect(Exception.stacktrace(inner_error))}"
              Logger.error("[#{run.id}] #{error_message}")
              Reconciliation.mark_reconciliation_failed(run, error_message)
          end
      end
    rescue
      outer_error ->
        # This catches errors from Reconciliation.get_reconciliation_run! (if used) or other unexpected issues before run is fetched.
        error_message = "Outer error in process_reconciliation for ID #{reconciliation_run_id}: #{Exception.message(outer_error)}\nStacktrace: #{inspect(Exception.stacktrace(outer_error))}"
        Logger.error(error_message)
        # If run_id is known, attempt to mark as failed by ID
        if reconciliation_run_id do
          Reconciliation.mark_reconciliation_failed_by_id(reconciliation_run_id, "Outer processing error: #{Exception.message(outer_error)}")
        end
    end
  end

  defp error_to_string(:too_large), do: "File too large (max 50MB)"
  defp error_to_string(:too_many_files), do: "Too many files"
  defp error_to_string(:not_accepted), do: "File type not accepted (.xlsx, .xls, .csv only)"
  defp error_to_string(_), do: "Unknown error"

  # Error Alert Component
  defp error_alert(assigns) do
    ~H"""
    <div class="bg-red-50 border border-red-200 rounded-lg p-3">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <.icon name="hero-exclamation-triangle" class="w-5 h-5 text-red-400" />
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-red-800">
            Upload Error
          </h3>
          <div class="mt-1 text-sm text-red-700">
            <%= error_to_string(@error) %>
          </div>
          <div class="mt-2">
            <button
              type="button"
              class="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-2 py-1 rounded transition-colors"
              phx-click="dismiss_error"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Format user-friendly error messages
  defp format_user_friendly_error(error) when is_binary(error) do
    cond do
      String.contains?(error, "Unsupported file format") ->
        "This file format is not supported. Please upload an Excel (.xlsx, .xls) or CSV file."

      String.contains?(error, "Failed to parse") ->
        "Unable to read the file. Please check that the file is not corrupted and try again."

      String.contains?(error, "File is empty") ->
        "The uploaded file appears to be empty. Please check your file and try again."

      String.contains?(error, "Missing or invalid amount") ->
        "Some rows are missing required amount values. Please check your data and try again."

      String.contains?(error, "no data") ->
        "No transaction data found in the file. Please check that your file contains the expected data."

      true ->
        "An error occurred while processing your file. Please try again or contact support if the problem persists."
    end
  end
  defp format_user_friendly_error(error), do: "An unexpected error occurred: #{inspect(error)}"

  # Format changeset errors for display
  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end

  # Upload Progress Card Component
  defp upload_progress_card(assigns) do
    ~H"""
    <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <!-- File Info Header -->
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <.icon name="hero-document" class="w-5 h-5 text-gray-400" />
          </div>
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900 truncate">
              <%= @entry.client_name %>
            </p>
            <p class="text-xs text-gray-500">
              <%= format_file_size(@entry.client_size) %>
            </p>
          </div>
        </div>
        <button
          type="button"
          phx-click="cancel_upload"
          phx-value-ref={@entry.ref}
          phx-value-type={@file_type}
          class="text-gray-400 hover:text-red-500 transition-colors"
        >
          <.icon name="hero-x-mark" class="w-4 h-4" />
        </button>
      </div>

      <!-- Upload Progress Section -->
      <div class="mb-4">
        <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
          <span class="font-medium">File Upload</span>
          <span class="font-mono"><%= @entry.progress %>%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3">
          <div
            class={"h-3 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"}
            style={"width: #{@entry.progress}%"}
          ></div>
        </div>

        <!-- Upload Stats -->
        <%= if @entry.progress > 0 and @entry.progress < 100 do %>
          <div class="grid grid-cols-2 gap-4 text-xs text-gray-500 mt-2">
            <div>
              <span class="font-medium">Speed:</span>
              <%= format_upload_speed(Map.get(@upload_status, :upload_speed, 0)) %>
            </div>
            <div>
              <span class="font-medium">ETA:</span>
              <%= format_eta(Map.get(@upload_status, :eta_seconds)) %>
            </div>
          </div>
        <% end %>

        <!-- Upload Status Messages -->
        <%= if @entry.progress == 100 do %>
          <div class="flex items-center text-xs text-green-600 mt-2">
            <.icon name="hero-check-circle" class="w-4 h-4 mr-1" />
            <span>Upload complete - Processing file...</span>
          </div>
        <% end %>
      </div>

      <!-- Database Processing Section -->
      <%= if @database_status do %>
        <div class="border-t border-gray-200 pt-4">
          <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
            <span class="font-medium">Database Processing</span>
            <%= if Map.get(@database_status, :progress) do %>
              <span class="font-mono"><%= @database_status.progress %>%</span>
            <% end %>
          </div>

          <.database_status_indicator status={@database_status} file_type={@file_type} />
        </div>
      <% end %>
    </div>
    """
  end

  # Database Status Indicator Component
  defp database_status_indicator(assigns) do
    ~H"""
    <div class="space-y-3">
      <%= case @status.status do %>
        <% "inserting" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-blue-600">
                <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                <span><%= @status.message || "Inserting records into database..." %></span>
              </div>
              <%= if Map.get(@status, :records_processed) && Map.get(@status, :total_records) do %>
                <span class="text-xs text-gray-500 font-mono">
                  <%= @status.records_processed %>/<%= @status.total_records %>
                </span>
              <% end %>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Processing records...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "parsing" -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-blue-600">
              <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
              <span><%= @status.message || "Parsing file structure..." %></span>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Reading file content...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "processing" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-blue-600">
                <.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                <span><%= @status.message || "Processing transaction data..." %></span>
              </div>
              <%= if Map.get(@status, :records_processed) && Map.get(@status, :total_records) do %>
                <span class="text-xs text-gray-500 font-mono">
                  <%= @status.records_processed %>/<%= @status.total_records %>
                </span>
              <% end %>
            </div>
            <%= if Map.has_key?(@status, :progress) and @status.progress > 0 do %>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class={"bg-blue-600 h-2 rounded-full transition-all duration-300 #{progress_bar_color(@file_type)}"} style={"width: #{@status.progress}%"}></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>Validating and storing records...</span>
                <span><%= @status.progress %>%</span>
              </div>
            <% end %>
          </div>

        <% "complete" -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-green-600">
              <.icon name="hero-check-circle" class="w-4 h-4 mr-2" />
              <span>Database processing complete!</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class={"bg-green-600 h-2 rounded-full #{progress_bar_color(@file_type)}"} style="width: 100%"></div>
            </div>
            <%= if Map.has_key?(@status, :result) do %>
              <div class="grid grid-cols-2 gap-4 text-xs text-gray-600">
                <%= if Map.get(@status.result, :rows_inserted) do %>
                  <div>
                    <span class="font-medium">Records:</span>
                    <%= @status.result.rows_inserted %>
                  </div>
                <% end %>
                <%= if Map.get(@status.result, :processing_time) do %>
                  <div>
                    <span class="font-medium">Time:</span>
                    <%= format_processing_time(@status.result.processing_time) %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>

        <% "error" -> %>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-red-600">
                <.icon name="hero-exclamation-triangle" class="w-4 h-4 mr-2" />
                <span>Processing failed</span>
              </div>
              <button
                type="button"
                phx-click="retry_upload"
                phx-value-file_id={get_file_id_from_status(@status)}
                class="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded transition-colors font-medium"
              >
                Retry
              </button>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-red-600 h-2 rounded-full" style="width: 100%"></div>
            </div>
            <%= if Map.has_key?(@status, :error) do %>
              <div class="bg-red-50 border border-red-200 rounded p-2">
                <div class="text-xs text-red-700 font-medium mb-1">Error Details:</div>
                <div class="text-xs text-red-600">
                  <%= @status.error %>
                </div>
              </div>
            <% end %>
          </div>

        <% _ -> %>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-gray-500">
              <.icon name="hero-clock" class="w-4 h-4 mr-2" />
              <span>Waiting to start database processing...</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gray-400 h-2 rounded-full" style="width: 0%"></div>
            </div>
          </div>
      <% end %>
    </div>
    """
  end

  # Helper functions for formatting
  defp format_file_size(bytes) when is_integer(bytes) do
    cond do
      bytes >= 1_000_000 -> "#{Float.round(bytes / 1_000_000, 1)} MB"
      bytes >= 1_000 -> "#{Float.round(bytes / 1_000, 1)} KB"
      true -> "#{bytes} bytes"
    end
  end
  defp format_file_size(_), do: "Unknown size"

  defp format_upload_speed(speed) when is_number(speed) and speed > 0 do
    cond do
      speed >= 1_000_000 -> "#{Float.round(speed / 1_000_000, 1)} MB/s"
      speed >= 1_000 -> "#{Float.round(speed / 1_000, 1)} KB/s"
      true -> "#{trunc(speed)} B/s"
    end
  end
  defp format_upload_speed(_), do: "Calculating..."

  defp format_eta(nil), do: "Calculating..."
  defp format_eta(seconds) when is_integer(seconds) and seconds > 0 do
    cond do
      seconds >= 60 -> "#{div(seconds, 60)}m #{rem(seconds, 60)}s"
      true -> "#{seconds}s"
    end
  end
  defp format_eta(_), do: "Almost done"

  defp progress_bar_color("file_a"), do: "bg-blue-600"
  defp progress_bar_color("file_b"), do: "bg-green-600"
  defp progress_bar_color(_), do: "bg-gray-600"

  defp format_processing_time(time_ms) when is_number(time_ms) do
    cond do
      time_ms >= 60_000 -> "#{Float.round(time_ms / 60_000, 1)}m"
      time_ms >= 1_000 -> "#{Float.round(time_ms / 1_000, 1)}s"
      true -> "#{trunc(time_ms)}ms"
    end
  end
  defp format_processing_time(_), do: "Unknown"

  # Helper function to get database status for a file entry
  defp get_database_status_for_entry(_database_status, nil), do: nil
  defp get_database_status_for_entry(database_status, uploaded_file) do
    Map.get(database_status, uploaded_file.id)
  end

  # Helper function to extract file ID from status (for retry functionality)
  defp get_file_id_from_status(status) do
    # This would need to be passed from the component context
    # For now, we'll use a placeholder
    Map.get(status, :file_id, "unknown")
  end

  # Handle dismiss error event
  @impl true
  def handle_event("dismiss_error", _params, socket) do
    {:noreply, clear_flash(socket, :error)}
  end

  # Generate a smart run name based on uploaded files
  defp generate_smart_run_name(socket) do
    # Get filenames from uploaded entries
    file_a_name = get_uploaded_filename(socket, :file_a)
    file_b_name = get_uploaded_filename(socket, :file_b)

    case {file_a_name, file_b_name} do
      {nil, nil} ->
        # No files uploaded yet, use fallback
        Reconciliation.generate_fallback_run_name()

      {file_a, nil} ->
        # Only file A uploaded
        name_a = Reconciliation.extract_meaningful_name(file_a)
        date_str = Date.utc_today() |> Calendar.strftime("%b %d, %Y")
        unique_id = Reconciliation.generate_short_unique_id()
        "#{name_a} - #{date_str} (#{unique_id})"

      {nil, file_b} ->
        # Only file B uploaded
        name_b = Reconciliation.extract_meaningful_name(file_b)
        date_str = Date.utc_today() |> Calendar.strftime("%b %d, %Y")
        unique_id = Reconciliation.generate_short_unique_id()
        "#{name_b} - #{date_str} (#{unique_id})"

      {file_a, file_b} ->
        # Both files uploaded, use smart naming
        Reconciliation.generate_run_name_from_files(file_a, file_b)
    end
  end

  # Helper to get filename from uploaded entries
  defp get_uploaded_filename(socket, upload_key) do
    case socket.assigns.uploads[upload_key].entries do
      [entry | _] -> entry.client_name
      [] -> nil
    end
  end

  # Generate a default run name with unique identifier (fallback)
  defp generate_default_run_name do
    today = Date.utc_today()
    unique_id = generate_short_id()
    "Reconciliation #{today}-#{unique_id}"
  end

  # Generate a short unique identifier (6 characters)
  defp generate_short_id do
    :crypto.strong_rand_bytes(3)
    |> Base.encode16(case: :lower)
  end
end
