defmodule ReconciliationWeb.ReportsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.{ReconciliationRun, Transaction, TransactionMatch}
  alias Reconciliation.Services.ActivityLogger

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Log page visit
    if connected?(socket) do
      ActivityLogger.log_data_access_activity(
        user.id,
        "page_visit",
        organization_id: user.organization_id,
        metadata: %{
          page: "reports",
          url: "/reports"
        }
      )
    end

    # Get reconciliation runs and calculate summary statistics
    reconciliation_runs = Reconciliation.list_reconciliation_runs(user.id)
    summary_stats = calculate_summary_stats(reconciliation_runs)

    {:ok,
     socket
     |> assign(:page_title, "Reports")
     |> assign(:reconciliation_runs, reconciliation_runs)
     |> assign(:all_reconciliation_runs, reconciliation_runs)
     |> assign(:summary_stats, summary_stats)
     |> assign(:selected_run_id, nil)
     |> assign(:export_states, %{})  # Track export states per run ID
    }
  end

  @impl true
  def handle_event("filter_by_run", %{"run_id" => ""}, socket) do
    # Show all runs
    all_runs = socket.assigns.all_reconciliation_runs
    summary_stats = calculate_summary_stats(all_runs)

    {:noreply,
     socket
     |> assign(:reconciliation_runs, all_runs)
     |> assign(:summary_stats, summary_stats)
     |> assign(:selected_run_id, nil)
    }
  end

  def handle_event("filter_by_run", %{"run_id" => run_id}, socket) do
    # Filter to show only the selected run
    run_id_int = String.to_integer(run_id)
    selected_run = Enum.find(socket.assigns.all_reconciliation_runs, &(&1.id == run_id_int))

    filtered_runs = if selected_run, do: [selected_run], else: []
    summary_stats = calculate_summary_stats(filtered_runs)

    {:noreply,
     socket
     |> assign(:reconciliation_runs, filtered_runs)
     |> assign(:summary_stats, summary_stats)
     |> assign(:selected_run_id, run_id_int)
    }
  end

  def handle_event("export_report", %{"run_id" => run_id}, socket) do
    run_id_int = String.to_integer(run_id)

    # Set export state to processing for this specific run
    export_states = Map.put(socket.assigns.export_states, run_id_int, "processing")
    socket = assign(socket, :export_states, export_states)

    try do
      run = Enum.find(socket.assigns.all_reconciliation_runs, &(&1.id == run_id_int))

      if run do
        # Get transactions and matches for this run
        transactions = Reconciliation.get_transactions(run_id_int)
        matches = Reconciliation.get_transaction_matches(run_id_int)

        # Generate Excel content for reconciliation report
        case Reconciliation.Services.ExcelExport.generate_reconciliation_report_excel(run, transactions, matches) do
          {:ok, excel_content} ->
            # Create filename with run name and date
            filename = "reconciliation_report_#{run.name}_#{Date.to_string(Date.utc_today())}.xlsx"

            # Set state to complete and schedule reset to idle
            Process.send_after(self(), {:reset_export_state, run_id_int}, 2000)

            export_states = Map.put(socket.assigns.export_states, run_id_int, "complete")

            # Send download response
            {:noreply,
             socket
             |> assign(:export_states, export_states)
             |> push_event("download_file", %{
               content: excel_content,
               filename: filename,
               content_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
             })
             |> put_flash(:info, "Report exported successfully!")
            }

          {:error, reason} ->
            # Set state to error and schedule reset to idle
            Process.send_after(self(), {:reset_export_state, run_id_int}, 3000)

            export_states = Map.put(socket.assigns.export_states, run_id_int, "error")

            {:noreply,
             socket
             |> assign(:export_states, export_states)
             |> put_flash(:error, "Export failed: #{reason}")
            }
        end
      else
        # Set state to error and schedule reset to idle
        Process.send_after(self(), {:reset_export_state, run_id_int}, 3000)

        export_states = Map.put(socket.assigns.export_states, run_id_int, "error")

        {:noreply,
         socket
         |> assign(:export_states, export_states)
         |> put_flash(:error, "Reconciliation run not found")
        }
      end
    rescue
      error ->
        # Set state to error and schedule reset to idle
        Process.send_after(self(), {:reset_export_state, run_id_int}, 3000)

        export_states = Map.put(socket.assigns.export_states, run_id_int, "error")

        {:noreply,
         socket
         |> assign(:export_states, export_states)
         |> put_flash(:error, "Export failed: #{Exception.message(error)}")
        }
    end
  end

  # Handle export state reset for specific run
  @impl true
  def handle_info({:reset_export_state, run_id}, socket) do
    export_states = Map.put(socket.assigns.export_states, run_id, "idle")
    {:noreply, assign(socket, :export_states, export_states)}
  end

  # Helper functions
  defp calculate_summary_stats(runs) do
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))
    
    total_runs = length(runs)
    completed_count = length(completed_runs)
    
    # For reconciliation, total transactions should be the sum of the maximum transactions
    # from each run (since matches pair one transaction from A with one from B)
    total_transactions = Enum.sum(Enum.map(completed_runs, &(max(&1.total_transactions_a, &1.total_transactions_b))))
    total_matched = Enum.sum(Enum.map(completed_runs, &(&1.matched_count)))
    
    avg_match_rate = if completed_count > 0 do
      completed_runs
      |> Enum.map(&Decimal.to_float(&1.match_rate))
      |> Enum.sum()
      |> Kernel./(completed_count)
      |> Float.round(1)
    else
      0.0
    end
    
    total_amount_processed = completed_runs
    |> Enum.map(&Decimal.add(&1.total_amount_a, &1.total_amount_b))
    |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
    
    %{
      total_runs: total_runs,
      completed_runs: completed_count,
      total_transactions: total_transactions,
      total_matched: total_matched,
      avg_match_rate: avg_match_rate,
      total_amount_processed: total_amount_processed
    }
  end

  defp filter_runs_by_days(runs, days) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-days, :day)
    Enum.filter(runs, &(DateTime.compare(&1.inserted_at, cutoff_date) == :gt))
  end

  defp format_currency(amount) when is_nil(amount), do: "0.00"
  defp format_currency(amount) do
    Decimal.to_string(amount, :normal)
  end

  defp format_currency_with_symbol(amount, currency) when is_nil(amount), do: "#{currency_symbol(currency)}0.00"
  defp format_currency_with_symbol(amount, currency) do
    "#{currency_symbol(currency)}#{Decimal.to_string(amount, :normal)}"
  end

  defp currency_symbol(currency) when is_binary(currency) do
    case String.upcase(currency) do
      "USD" -> "$"
      "EUR" -> "€"
      "GBP" -> "£"
      "JPY" -> "¥"
      "CAD" -> "C$"
      "AUD" -> "A$"
      "MWK" -> "MK "
      "ZAR" -> "R "
      "KES" -> "KSh "
      "TZS" -> "TSh "
      "UGX" -> "USh "
      "ZMW" -> "ZK "
      "BWP" -> "P "
      "NAD" -> "N$ "
      "SZL" -> "L "
      "LSL" -> "M "
      other -> "#{other} "
    end
  end
  defp currency_symbol(_), do: ""

  defp format_percentage(percentage) when is_nil(percentage), do: "0%"
  defp format_percentage(percentage) when is_float(percentage), do: "#{percentage}%"
  defp format_percentage(percentage) do
    "#{Decimal.to_string(percentage, :normal)}%"
  end

  defp status_color("completed"), do: "green"
  defp status_color("processing"), do: "yellow"
  defp status_color("failed"), do: "red"
  defp status_color(_), do: "gray"

  defp status_icon("completed"), do: "hero-check-circle"
  defp status_icon("processing"), do: "hero-clock"
  defp status_icon("failed"), do: "hero-x-circle"
  defp status_icon(_), do: "hero-question-mark-circle"

  defp match_rate_color(rate) when rate >= 90, do: "text-green-600"
  defp match_rate_color(rate) when rate >= 70, do: "text-yellow-600"
  defp match_rate_color(_), do: "text-red-600"

  # Generate CSV content for reconciliation report
  defp generate_reconciliation_report_csv(run, transactions, matches) do
    # CSV headers
    headers = [
      "Transaction Date",
      "Transaction ID",
      "Description",
      "Reference",
      "Amount",
      "Currency",
      "File Source",
      "Match Status",
      "Match Confidence",
      "Match Type",
      "Matched Transaction ID",
      "Verified"
    ]

    # Convert transactions to CSV rows with match information
    rows = Enum.map(transactions, fn transaction ->
      # Find if this transaction has a match
      match_info = find_transaction_match(transaction, matches)

      [
        format_date_for_export(transaction.transaction_date),
        transaction.transaction_id || "",
        transaction.description || "",
        transaction.reference || "",
        Decimal.to_string(transaction.amount),
        transaction.currency || "USD",
        get_file_source(transaction, run),
        if(transaction.is_matched, do: "Matched", else: "Unmatched"),
        if(match_info, do: Decimal.to_string(match_info.confidence_score), else: ""),
        if(match_info, do: match_info.match_type, else: ""),
        if(match_info, do: get_matched_transaction_id(transaction, match_info), else: ""),
        if(match_info && match_info.verified_by_user, do: "Yes", else: "No")
      ]
    end)

    # Combine headers and rows, then convert to CSV string
    [headers | rows]
    |> Enum.map(&Enum.join(&1, ","))
    |> Enum.join("\n")
  end

  defp find_transaction_match(transaction, matches) do
    Enum.find(matches, fn match ->
      match.transaction_a_id == transaction.id || match.transaction_b_id == transaction.id
    end)
  end

  defp get_file_source(transaction, run) do
    cond do
      transaction.uploaded_file && String.contains?(transaction.uploaded_file.filename, run.file_a_name || "") -> "File A"
      transaction.uploaded_file && String.contains?(transaction.uploaded_file.filename, run.file_b_name || "") -> "File B"
      true -> "Unknown"
    end
  end

  defp get_matched_transaction_id(transaction, match) do
    if match.transaction_a_id == transaction.id do
      to_string(match.transaction_b_id)
    else
      to_string(match.transaction_a_id)
    end
  end

  defp format_date_for_export(nil), do: ""
  defp format_date_for_export(date) when is_binary(date), do: date
  defp format_date_for_export(%Date{} = date), do: Date.to_string(date)
  defp format_date_for_export(%DateTime{} = datetime), do: DateTime.to_date(datetime) |> Date.to_string()
  defp format_date_for_export(%NaiveDateTime{} = datetime), do: NaiveDateTime.to_date(datetime) |> Date.to_string()
end
