<div class="max-w-4xl mx-auto p-6">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Reconciliation Settings</h1>
    <p class="text-gray-600">Configure your reconciliation preferences and matching criteria</p>
  </div>

  <.form for={@form} phx-change="validate" phx-submit="save" class="space-y-8">
    <!-- Matching Criteria -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-6">Matching Criteria</h2>

      <div class="max-w-md">
        <div>
          <.label for={@form[:amount_tolerance].id}>Amount Tolerance</.label>
          <.input
            field={@form[:amount_tolerance]}
            type="number"
            step="0.01"
            min="0"
            placeholder="0.01"
          />
          <p class="mt-1 text-sm text-gray-500">
            Maximum difference allowed for amount matching (e.g., 0.01 for $0.01)
          </p>
        </div>
      </div>
    </div>



    <!-- Action Buttons -->
    <div class="flex justify-between items-center pt-6 border-t border-gray-200">
      <button
        type="button"
        phx-click="reset_defaults"
        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium"
      >
        Reset to Defaults
      </button>

      <div class="flex space-x-3">
        <.link navigate={~p"/dashboard"} class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg font-medium">
          Cancel
        </.link>
        <.button type="submit" phx-disable-with="Saving..." class="bg-indigo-600 hover:bg-indigo-700 text-white">
          Save Settings
        </.button>
      </div>
    </div>
  </.form>
</div>
