defmodule ReconciliationWeb.SummaryLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation
  alias Reconciliation.Services.TransactionService

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Get comprehensive summary data
    summary_data = Reconciliation.get_comprehensive_summary(user.id)

    # Get available runs for specific run filtering (recent 100 runs)
    reconciliation_runs = Reconciliation.list_reconciliation_runs(user.id)
    |> Enum.take(100)  # Limit to recent 100 runs for performance

    # Calculate transaction totals using service
    transaction_totals = TransactionService.calculate_transaction_totals(user.id, nil)

    {:ok,
     socket
     |> assign(:page_title, "Summary")
     |> assign(:current_path, "/summary")
     |> assign(:summary_data, summary_data)
     |> assign(:selected_run_id, nil)
     |> assign(:reconciliation_runs, reconciliation_runs)
     |> assign(:transaction_totals, transaction_totals)
     |> assign(:loading, false)
    }
  end



  @impl true
  def handle_event("filter_by_run", %{"run_id" => run_id}, socket) do
    user = socket.assigns.current_user

    # Show loading state
    socket = assign(socket, :loading, true)

    case run_id do
      "" ->
        # Reset to show all runs
        summary_data = Reconciliation.get_comprehensive_summary(user.id)
        transaction_totals = TransactionService.calculate_transaction_totals(user.id, nil)

        {:noreply,
         socket
         |> assign(:summary_data, summary_data)
         |> assign(:selected_run_id, nil)
         |> assign(:transaction_totals, transaction_totals)
         |> assign(:loading, false)
        }

      _ ->
        # Filter to specific run
        run_id_int = String.to_integer(run_id)
        summary_data = Reconciliation.get_comprehensive_summary(user.id, run_filter: run_id_int)
        transaction_totals = TransactionService.calculate_transaction_totals(user.id, run_id_int)

        {:noreply,
         socket
         |> assign(:summary_data, summary_data)
         |> assign(:selected_run_id, run_id)  # Keep as string for UI
         |> assign(:transaction_totals, transaction_totals)
         |> assign(:loading, false)
        }
    end
  end

  @impl true
  def handle_event("refresh_data", _params, socket) do
    user = socket.assigns.current_user

    # Show loading state
    socket = assign(socket, :loading, true)

    # Get fresh summary data with current filters
    opts = if socket.assigns.selected_run_id do
      [run_filter: String.to_integer(socket.assigns.selected_run_id)]
    else
      []
    end

    run_filter = if socket.assigns.selected_run_id do
      String.to_integer(socket.assigns.selected_run_id)
    else
      nil
    end

    summary_data = Reconciliation.get_comprehensive_summary(user.id, opts)
    transaction_totals = TransactionService.calculate_transaction_totals(user.id, run_filter)

    {:noreply,
     socket
     |> assign(:summary_data, summary_data)
     |> assign(:transaction_totals, transaction_totals)
     |> assign(:loading, false)
     |> put_flash(:info, "Summary data refreshed successfully!")
    }
  end

  # Helper functions for formatting

  def format_currency(amount) when is_nil(amount), do: "0.00"
  def format_currency(amount) do
    Decimal.to_string(amount, :normal)
  end

  def format_currency_with_symbol(amount, currency) when is_nil(amount), do: "#{currency_symbol(currency)}0.00"
  def format_currency_with_symbol(amount, currency) do
    "#{currency_symbol(currency)}#{Decimal.to_string(amount, :normal)}"
  end

  def format_number(number) when is_nil(number), do: "0"
  def format_number(number) do
    # Simple number formatting without external dependencies
    number
    |> to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  def format_date(nil), do: "-"
  def format_date(%DateTime{} = datetime) do
    Calendar.strftime(datetime, "%b %d, %Y")
  end
  def format_date(%Date{} = date) do
    Calendar.strftime(date, "%b %d, %Y")
  end

  def format_currency_with_sign(amount) when is_nil(amount), do: "0.00"
  def format_currency_with_sign(amount) do
    if Decimal.negative?(amount) do
      "-#{Decimal.to_string(Decimal.abs(amount), :normal)}"
    else
      "+#{Decimal.to_string(amount, :normal)}"
    end
  end

  defp currency_symbol(currency) when is_binary(currency) do
    case String.upcase(currency) do
      "USD" -> "$"
      "EUR" -> "€"
      "GBP" -> "£"
      "JPY" -> "¥"
      "CAD" -> "C$"
      "AUD" -> "A$"
      "MWK" -> "MK "
      "ZAR" -> "R "
      "KES" -> "KSh "
      "TZS" -> "TSh "
      "UGX" -> "USh "
      "ZMW" -> "ZK "
      "BWP" -> "P "
      "NAD" -> "N$ "
      "SZL" -> "L "
      "LSL" -> "M "
      other -> "#{other} "
    end
  end
  defp currency_symbol(_), do: ""

  # Transaction calculation functions moved to TransactionService

  # Function moved to TransactionService

  # Function moved to TransactionService

  # Function moved to TransactionService

  def format_file_size(size_bytes) when is_nil(size_bytes) or size_bytes == 0, do: "0 B"
  def format_file_size(size_bytes) do
    cond do
      size_bytes >= 1_073_741_824 -> "#{Float.round(size_bytes / 1_073_741_824, 2)} GB"
      size_bytes >= 1_048_576 -> "#{Float.round(size_bytes / 1_048_576, 2)} MB"
      size_bytes >= 1024 -> "#{Float.round(size_bytes / 1024, 2)} KB"
      true -> "#{size_bytes} B"
    end
  end

  def format_percentage(percentage) when is_nil(percentage), do: "0%"
  def format_percentage(percentage) do
    "#{Float.round(percentage, 1)}%"
  end

  def format_number(number) when is_nil(number), do: "0"
  def format_number(number) do
    # Simple number formatting without external dependencies
    number
    |> to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  def format_date(nil), do: "N/A"
  def format_date(datetime) do
    Calendar.strftime(datetime, "%b %d, %Y")
  end

  def format_difference(difference) when is_nil(difference), do: "0.00"
  def format_difference(difference) do
    if Decimal.negative?(difference) do
      "-#{Decimal.to_string(Decimal.abs(difference), :normal)}"
    else
      "+#{Decimal.to_string(difference, :normal)}"
    end
  end

  def format_difference_with_symbol(difference, currency) when is_nil(difference), do: "#{currency_symbol(currency)}0.00"
  def format_difference_with_symbol(difference, currency) do
    symbol = currency_symbol(currency)
    if Decimal.negative?(difference) do
      "-#{symbol}#{Decimal.to_string(Decimal.abs(difference), :normal)}"
    else
      "+#{symbol}#{Decimal.to_string(difference, :normal)}"
    end
  end

  def difference_color(difference) do
    cond do
      is_nil(difference) or Decimal.equal?(difference, Decimal.new("0")) -> "text-gray-500"
      Decimal.positive?(difference) -> "text-green-400"
      true -> "text-red-400"
    end
  end

  def difference_bg_color(difference) do
    cond do
      is_nil(difference) or Decimal.equal?(difference, Decimal.new("0")) -> "bg-gray-50"
      Decimal.positive?(difference) -> "bg-green-50"
      true -> "bg-red-50"
    end
  end

  def status_badge_class("completed"), do: "bg-green-100 text-green-800"
  def status_badge_class("pending"), do: "bg-yellow-100 text-yellow-800"
  def status_badge_class("failed"), do: "bg-red-100 text-red-800"
  def status_badge_class("processing"), do: "bg-blue-100 text-blue-800"
  def status_badge_class(_), do: "bg-gray-100 text-gray-800"

  def file_type_display("file_a"), do: "First Files (A)"
  def file_type_display("file_b"), do: "Second Files (B)"
  def file_type_display(type), do: String.capitalize(type)

  def clean_file_name(filename) when is_binary(filename) do
    # Remove file extension
    clean_name = Path.basename(filename, Path.extname(filename))

    # Replace underscores with spaces and clean up
    clean_name
    |> String.replace("_", " ")
    |> String.replace("-", " ")
    |> String.split()
    |> Enum.map(&String.capitalize/1)
    |> Enum.join(" ")
  end
  def clean_file_name(_), do: "Unknown File"



  def has_data?(summary_data) do
    summary_data.summary_info.total_reconciliation_runs > 0
  end

  def get_metric_trend_class(current, previous) when is_nil(previous) or previous == 0, do: "text-gray-500"
  def get_metric_trend_class(current, previous) when current > previous, do: "text-green-600"
  def get_metric_trend_class(current, previous) when current < previous, do: "text-red-600"
  def get_metric_trend_class(_, _), do: "text-gray-500"

  def calculate_trend_percentage(current, previous) when is_nil(previous) or previous == 0, do: nil
  def calculate_trend_percentage(current, previous) do
    ((current - previous) / previous * 100) |> Float.round(1)
  end

  # Function moved to TransactionService
end
