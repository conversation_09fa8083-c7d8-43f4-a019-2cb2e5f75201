defmodule ReconciliationWeb.UserForgotPasswordLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.Accounts

  def render(assigns) do
    ~H"""
    <style>
      :root {
        /* ProBASE Professional Color Scheme - Dark Slate Gray Theme */
        --primary-gradient: linear-gradient(135deg, #2f4f4f 0%, #3a5a5a 100%);
        --secondary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        --accent-gradient: linear-gradient(135deg, #4a6a6a 0%, #3a5a5a 100%);

        /* Background Colors */
        --light-bg: #2f4f4f;
        --lighter-bg: #3a5a5a;
        --section-bg: #4a6a6a;
        --dark-bg: #253f3f;
        --darker-bg: #1a2f2f;
        --content-bg: #ffffff;

        /* Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-bg-dark: rgba(47, 79, 79, 0.95);
        --glass-border: rgba(100, 116, 139, 0.2);
        --glass-border-light: rgba(226, 232, 240, 0.8);

        /* Text Colors */
        --text-primary: #0d1421;
        --text-primary-light: #ffffff;
        --text-secondary: #64748b;
        --text-secondary-light: #94a3b8;

        /* ProBASE Brand Colors */
        --probase-primary: #1a237e;
        --probase-secondary: #f97316;
        --probase-accent: #3f51b5;
        --probase-dark: #0d1421;
        --probase-light: #f8fafc;
        --probase-gray: #64748b;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: var(--light-bg);
        color: var(--text-primary-light);
        overflow-x: hidden;
        line-height: 1.6;
        margin: 0;
        padding: 0;
      }

      /* Animated Background */
      .forgot-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: radial-gradient(circle at 20% 50%, rgba(249, 115, 22, 0.05) 0%, transparent 50%),
                   radial-gradient(circle at 80% 20%, rgba(74, 106, 106, 0.08) 0%, transparent 50%),
                   radial-gradient(circle at 40% 80%, rgba(249, 115, 22, 0.03) 0%, transparent 50%);
        animation: float 20s ease-in-out infinite;
      }

      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(1deg); }
        66% { transform: translateY(-10px) rotate(-1deg); }
      }

      /* Forgot Password Container */
      .forgot-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        position: relative;
      }

      .forgot-card {
        background: var(--glass-bg);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 1px solid var(--glass-border-light);
        border-radius: 24px;
        box-shadow: 0 25px 50px rgba(107, 114, 128, 0.15);
        padding: 3rem;
        width: 100%;
        max-width: 480px;
        position: relative;
        overflow: hidden;
      }

      .forgot-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--secondary-gradient);
        border-radius: 24px 24px 0 0;
      }

      /* Logo and Header */
      .forgot-header {
        text-align: center;
        margin-bottom: 2.5rem;
      }

      .forgot-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        font-size: 1.5rem;
        font-weight: 700;
        background: var(--accent-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .forgot-logo img {
        height: 48px;
        width: auto;
        margin-right: 12px;
      }

      .forgot-title {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, var(--text-primary) 0%, var(--probase-secondary) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .forgot-subtitle {
        color: var(--text-secondary);
        font-size: 1rem;
        line-height: 1.5;
      }

      /* Form Styling */
      .forgot-form {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }

      .form-group {
        position: relative;
      }

      .form-label {
        display: block;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .form-input {
        width: 100%;
        padding: 1rem 1.25rem;
        border: 2px solid rgba(100, 116, 139, 0.2);
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.8);
        font-size: 1rem;
        transition: all 0.3s ease;
        box-sizing: border-box;
      }

      .form-input:focus {
        outline: none;
        border-color: var(--probase-secondary);
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        background: rgba(255, 255, 255, 0.95);
      }

      .form-input::placeholder {
        color: var(--text-secondary);
      }

      /* Override Phoenix Core Component Dark Theme Styling */
      .forgot-form input[type="email"] {
        background: #ffffff !important;
        color: #374151 !important;
        border: 2px solid rgba(100, 116, 139, 0.2) !important;
        border-radius: 12px !important;
      }

      .forgot-form input[type="email"]:focus {
        background: #ffffff !important;
        border-color: var(--probase-secondary) !important;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
      }

      /* Ensure all Phoenix input wrappers are white with border radius */
      .forgot-form div[phx-feedback-for],
      .forgot-card div[phx-feedback-for],
      .forgot-form .phx-form-error,
      .forgot-card .phx-form-error {
        background-color: #ffffff !important;
        border-radius: 12px !important;
      }

      /* Target any Phoenix wrapper divs and make them white */
      .forgot-form div,
      .forgot-card div {
        background-color: #ffffff !important;
        border-radius: inherit;
      }

      /* Specific Phoenix component wrappers */
      .forgot-form .phx-input,
      .forgot-card .phx-input,
      .forgot-form [data-phx-component],
      .forgot-card [data-phx-component] {
        background-color: #ffffff !important;
        border-radius: 12px !important;
      }

      /* Additional overrides for Tailwind classes */
      .forgot-form .bg-gray-800,
      .forgot-card .bg-gray-800 {
        background-color: #ffffff !important;
        border-radius: 12px !important;
      }

      .forgot-form .text-white,
      .forgot-card .text-white {
        color: #374151 !important;
      }

      /* Button Styling */
      .forgot-button {
        width: 100%;
        padding: 1rem 2rem;
        background: var(--secondary-gradient);
        color: white;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 1rem;
      }

      .forgot-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(249, 115, 22, 0.3);
      }

      .forgot-button:active {
        transform: translateY(0);
      }

      .forgot-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }

      /* Footer Links */
      .forgot-footer {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(100, 116, 139, 0.2);
      }

      .nav-links {
        color: var(--text-secondary);
        font-size: 0.9rem;
        display: flex;
        justify-content: center;
        gap: 1rem;
      }

      .nav-links a {
        color: var(--probase-secondary);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .nav-links a:hover {
        color: var(--probase-secondary);
        text-decoration: underline;
      }

      /* Responsive Design */
      @media (max-width: 640px) {
        .forgot-container {
          padding: 1rem;
        }

        .forgot-card {
          padding: 2rem;
        }

        .forgot-title {
          font-size: 1.75rem;
        }
      }
    </style>

    <div class="forgot-bg"></div>

    <div class="forgot-container">
      <div class="forgot-card">
        <div class="forgot-header">
          <div class="forgot-logo">
            <img src="/images/probase-logo.png" alt="ProBASE Logo" />
            ProBASE
          </div>
          <h1 class="forgot-title">Reset Password</h1>
          <p class="forgot-subtitle">We'll send a password reset link to your inbox</p>
        </div>

        <.simple_form for={@form} id="reset_password_form" phx-submit="send_email" class="forgot-form">
          <div class="form-group">
            <label for="user_email" class="form-label">Email Address</label>
            <.input field={@form[:email]} type="email" placeholder="Enter your email" required class="form-input" />
          </div>

          <:actions>
            <.button phx-disable-with="Sending..." class="forgot-button">
              Send Reset Instructions
            </.button>
          </:actions>
        </.simple_form>

        <div class="forgot-footer">
          <div class="nav-links">
            <.link href={~p"/users/register"}>Create Account</.link>
            <span>|</span>
            <.link href={~p"/users/log_in"}>Back to Sign In</.link>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def mount(_params, _session, socket) do
    {:ok, assign(socket, form: to_form(%{}, as: "user"), page_title: "Forgot Password")}
  end

  def handle_event("send_email", %{"user" => %{"email" => email}}, socket) do
    case Accounts.get_user_by_email(email) do
      nil ->
        # Don't reveal whether the email exists or not for security
        info = "If an account with that email exists, you will receive password reset instructions shortly."

        {:noreply,
         socket
         |> put_flash(:info, info)
         |> redirect(to: ~p"/")}

      user ->
        case Accounts.deliver_user_reset_password_instructions(
               user,
               &url(~p"/users/reset_password/#{&1}")
             ) do
          {:ok, _} ->
            info = "Password reset instructions have been sent to your email address."

            {:noreply,
             socket
             |> put_flash(:info, info)
             |> redirect(to: ~p"/")}

          {:error, _reason} ->
            error = "There was an error sending the password reset email. Please try again or contact support."

            {:noreply,
             socket
             |> put_flash(:error, error)
             |> assign(form: to_form(%{}, as: "user"))}
        end
    end
  end
end
