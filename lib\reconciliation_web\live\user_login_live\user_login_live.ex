defmodule ReconciliationWeb.UserLoginLive do
  use ReconciliationWeb, :live_view

  def render(assigns) do
    ~H"""
    <style>
      /* Reset margins and padding for perfect centering */
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        overflow: hidden;
      }

      :root {
        /* ProBASE Professional Color Scheme - Light Theme */
        --primary-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        --secondary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        --accent-gradient: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);

        /* Background Colors */
        --light-bg: #ffffff;
        --lighter-bg: #f8fafc;
        --section-bg: #f1f5f9;
        --dark-bg: #e2e8f0;
        --darker-bg: #cbd5e1;
        --content-bg: #ffffff;

        /* Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-bg-dark: rgba(248, 250, 252, 0.95);
        --glass-border: rgba(7, 37, 80, 0.2);
        --glass-border-light: rgba(226, 232, 240, 0.8);

        /* Text Colors */
        --text-primary: #0d1421;
        --text-primary-light: #1e293b;
        --text-secondary: #64748b;
        --text-secondary-light: #475569;

        /* ProBASE Brand Colors */
        --probase-primary: #1a237e;
        --probase-secondary: #f97316;
        --probase-accent: #3f51b5;
        
        --probase-light: #f8fafc;
        --probase-gray: #64748b;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: #ffffff;
        color: var(--text-primary);
        overflow: hidden;
        line-height: 1.6;
        margin: 0;
        padding: 0;
        height: 100vh;
      }

      /* Clean Background - No Animation */
      .login-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: #ffffff;
      }

      /* Login Container */
      .login-container {
        height: 100vh;
        width: 100vw;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        position: fixed;
        top: 0;
        left: 0;
        overflow: hidden;
        box-sizing: border-box;
      }

      .login-card {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        width: 100%;
        max-width: 420px;
        max-height: 90vh;
        position: relative;
        overflow-y: auto;
        margin: auto;
        box-sizing: border-box;
      }

      .login-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--secondary-gradient);
        border-radius: 24px 24px 0 0;
      }

      /* Logo and Header */
      .login-header {
        text-align: center;
        margin-bottom: 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .login-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.3rem;
        font-weight: 700;
        color: #64748b;
      }

      .login-logo img {
        height: 40px;
        width: auto;
        margin-right: 10px;
      }

      .login-title {
        font-size: 1.6rem;
        font-weight: 800;
        margin-bottom: 0.25rem;
        color: #1e293b;
      }

      .login-subtitle {
        color: #64748b;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      /* Form Styling */
      .login-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        width: 100%;
        align-items: center;
        justify-content: center;
      }

      .form-group {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .form-label {
        display: block;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid #ffffff;
        border-radius: 8px;
        background: #ffffff;
        color: #1e293b;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        box-sizing: border-box;
      }

      .form-input:focus {
        outline: none;
        border-color: #f97316;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        background: #ffffff;
      }

      .form-input::placeholder {
        color: #64748b;
      }

      /* Ensure input text is always visible */
      .login-form input,
      .login-card input {
        color:  #1e293b !important;
        background-color: #ffffff !important;
      }

      /* Override Phoenix Core Component Dark Theme Styling - More Specific Selectors */
      .login-form input[type="email"],
      .login-form input[type="password"],
      .login-form input.form-input,
      .login-card input[type="email"],
      .login-card input[type="password"] {
        background: #ffffff !important;
        background-color: #ffffff !important;
        color: #ffffff; !important;
        border: 1px solid #ffffff; !important;
        border-radius: 8px !important;
        margin-top: 0.5rem !important;
      }

      .login-form input[type="email"]:focus,
      .login-form input[type="password"]:focus,
      .login-form input.form-input:focus,
      .login-card input[type="email"]:focus,
      .login-card input[type="password"]:focus {
        background: #ffffff !important;
        background-color: #ffffff !important;
        color: #1e293b !important;
        border-color: #f97316 !important;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
      }

      /* Additional overrides for Tailwind classes */
      .login-form .bg-gray-800,
      .login-card .bg-gray-800 {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      .login-form .text-white,
      .login-card .text-white {
        color: #1e293b !important;
      }

      /* Ensure all Phoenix input wrappers are white with border radius */
      .login-form div[phx-feedback-for],
      .login-card div[phx-feedback-for],
      .login-form .phx-form-error,
      .login-card .phx-form-error {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      /* Target any Phoenix wrapper divs and make them white */
      .login-form div,
      .login-card div {
        background-color: #ffffff !important;
        border-radius: inherit;
      }

      /* Specific Phoenix component wrappers */
      .login-form .phx-input,
      .login-card .phx-input,
      .login-form [data-phx-component],
      .login-card [data-phx-component] {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      /* Override Phoenix Core Component Label Styling */
      .login-form label {
        color: #1e293b !important;
        font-weight: 600 !important;
        font-size: 0.9rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        margin-bottom: 0.5rem !important;
      }

      /* Checkbox Styling */
      .checkbox-group {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0.5rem 0;
        width: 100%;
      }

      .checkbox-wrapper {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .checkbox-input {
        width: 18px;
        height: 18px;
        accent-color: var(--probase-secondary);
      }

      .checkbox-label {
        color: #1e293b !important;
        font-size: 0.9rem;
        cursor: pointer;
      }

      .forgot-link {
        color: #f97316;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .forgot-link:hover {
        color: #ea580c;
        text-decoration: underline;
      }

      /* Button Styling */
      .login-button {
        width: auto;
        min-width: 200px;
        max-width: 280px;
        padding: 0.75rem 2rem;
        background: var(--secondary-gradient);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 0.95rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin: 0.5rem auto 0 auto;
        display: block;
      }

      .login-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(249, 115, 22, 0.3);
      }

      .login-button:active {
        transform: translateY(0);
      }

      .login-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }

      /* Footer Links */
      .login-footer {
        text-align: center;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid rgba(100, 116, 139, 0.2);
      }

      .signup-link {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }

      .signup-link a {
        color: var(--probase-secondary);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .signup-link a:hover {
        color: var(--probase-secondary);
        text-decoration: underline;
      }

      /* Responsive Design */
      @media (max-width: 640px) {
        .login-container {
          padding: 1rem;
        }

        .login-card {
          padding: 2rem;
        }

        .login-title {
          font-size: 1.75rem;
        }
      }

      /* Error Styling */
      .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.5rem;
      }

      /* Loading State */
      .loading {
        opacity: 0.7;
        pointer-events: none;
      }

      .loading .login-button {
        background: var(--text-secondary);
      }

      /* Error Message Styling */
      .error-message {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 1px solid #fca5a5;
        border-radius: 8px;
        padding: 12px 16px;
        margin-bottom: 20px;
        animation: slideInError 0.3s ease-out;
      }

      .error-content {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #dc2626;
        font-size: 14px;
        font-weight: 500;
      }

      .error-icon {
        width: 20px;
        height: 20px;
        flex-shrink: 0;
        color: #dc2626;
      }

      @keyframes slideInError {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Add red border to inputs when there's an error */
      .error-message ~ .form-group .form-input {
        border-color: #fca5a5;
        box-shadow: 0 0 0 3px rgba(252, 165, 165, 0.1);
      }

      .error-message ~ .form-group .form-input:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
      }
    </style>

    <div class="login-bg"></div>

    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <div class="login-logo">
            <img src="/images/probase-logo.png" alt="ProBASE Logo" />
            
          </div>
          <h1 class="login-title">Welcome </h1>
          <p class="login-subtitle">Sign in to your  account</p>
        </div>

        <.simple_form for={@form} id="login_form" action={~p"/users/log_in"} phx-update="ignore" class="login-form">
          <!-- Display error message if login failed -->
          <div :if={Phoenix.Flash.get(@flash, :error)} class="error-message">
            <div class="error-content">
              <svg class="error-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <span><%= Phoenix.Flash.get(@flash, :error) %></span>
            </div>
          </div>

          <div class="form-group">
            <label for="user_email" class="form-label">Email Address</label>
            <.input field={@form[:email]} type="email" required class="form-input" placeholder="Enter your email" />
          </div>

          <div class="form-group">
            <label for="user_password" class="form-label">Password</label>
            <.input field={@form[:password]} type="password" required class="form-input" placeholder="Enter your password" />
          </div>

          <div class="checkbox-group">

            <.link href={~p"/users/reset_password"} class="forgot-link">
              Forgot password?
            </.link>
          </div>

          <:actions>
            <.button phx-disable-with="Signing in..." class="login-button">
              Sign In <span aria-hidden="true">→</span>
            </.button>
          </:actions>
        </.simple_form>

       
      </div>
    </div>
    """
  end

  def mount(_params, _session, socket) do
    email = Phoenix.Flash.get(socket.assigns.flash, :email)
    form = to_form(%{"email" => email}, as: "user")
    {:ok, assign(socket, form: form, page_title: "Sign In"), temporary_assigns: [form: form]}
  end
end