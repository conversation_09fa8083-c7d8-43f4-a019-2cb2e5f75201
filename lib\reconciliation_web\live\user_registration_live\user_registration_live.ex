defmodule ReconciliationWeb.UserRegistrationLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.Accounts
  alias Reconciliation.Accounts.User

  def render(assigns) do
    ~H"""
    <style>
      :root {
        /* ProBASE Professional Color Scheme - Dark Grey Slate Theme */
        --primary-gradient: linear-gradient(135deg, #2f3349 0%, #3a3f5c 100%);
        --secondary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        --accent-gradient: linear-gradient(135deg, #4a4f6a 0%, #3a3f5c 100%);

        /* Background Colors */
        --light-bg: #2f3349;
        --lighter-bg: #3a3f5c;
        --section-bg: #4a4f6a;
        --dark-bg: #252a3f;
        --darker-bg: #1a1f2f;
        --content-bg: #ffffff;

        /* Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-bg-dark: rgba(47, 79, 79, 0.95);
        --glass-border: rgba(100, 116, 139, 0.2);
        --glass-border-light: rgba(226, 232, 240, 0.8);

        /* Text Colors */
        --text-primary: #0d1421;
        --text-primary-light: #ffffff;
        --text-secondary: #64748b;
        --text-secondary-light: #94a3b8;

        /* ProBASE Brand Colors */
        --probase-primary: #1a237e;
        --probase-secondary: #f97316;
        --probase-accent: #3f51b5;
        --probase-dark: #0d1421;
        --probase-light: #f8fafc;
        --probase-gray: #64748b;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: var(--light-bg);
        color: var(--text-primary-light);
        overflow-x: hidden;
        line-height: 1.6;
        margin: 0;
        padding: 0;
      }

      /* Animated Background */
      .register-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: radial-gradient(circle at 20% 50%, rgba(249, 115, 22, 0.05) 0%, transparent 50%),
                   radial-gradient(circle at 80% 20%, rgba(74, 106, 106, 0.08) 0%, transparent 50%),
                   radial-gradient(circle at 40% 80%, rgba(249, 115, 22, 0.03) 0%, transparent 50%);
        animation: float 20s ease-in-out infinite;
      }

      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(1deg); }
        66% { transform: translateY(-10px) rotate(-1deg); }
      }

      /* Register Container */
      .register-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        position: relative;
      }

      .register-card {
        background: var(--glass-bg);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 1px solid var(--glass-border-light);
        border-radius: 24px;
        box-shadow: 0 25px 50px rgba(107, 114, 128, 0.15);
        padding: 2rem;
        width: 100%;
        max-width: 420px;
        position: relative;
        overflow: hidden;
      }

      .register-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--secondary-gradient);
        border-radius: 24px 24px 0 0;
      }

      /* Logo and Header */
      .register-header {
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .register-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.3rem;
        font-weight: 700;
        background: var(--accent-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .register-logo img {
        height: 40px;
        width: auto;
        margin-right: 10px;
      }

      .register-title {
        font-size: 1.6rem;
        font-weight: 800;
        margin-bottom: 0.25rem;
        background: linear-gradient(135deg, var(--text-primary) 0%, var(--probase-secondary) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .register-subtitle {
        color: var(--text-secondary);
        font-size: 0.9rem;
        line-height: 1.4;
      }

      /* Form Styling */
      .register-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .form-group {
        position: relative;
      }

      .form-label {
        display: block;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid rgba(100, 116, 139, 0.2);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.8);
        font-size: 0.95rem;
        transition: all 0.3s ease;
        box-sizing: border-box;
      }

      .form-input:focus {
        outline: none;
        border-color: var(--probase-secondary);
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        background: rgba(255, 255, 255, 0.95);
      }

      .form-input::placeholder {
        color: var(--text-secondary);
      }

      /* Override Phoenix Core Component Dark Theme Styling */
      .register-form input[type="email"],
      .register-form input[type="password"] {
        background: #ffffff !important;
        color: #374151 !important;
        border: 2px solid rgba(100, 116, 139, 0.2) !important;
        border-radius: 8px !important;
      }

      .register-form input[type="email"]:focus,
      .register-form input[type="password"]:focus {
        background: #ffffff !important;
        border-color: var(--probase-secondary) !important;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
      }

      /* Ensure all Phoenix input wrappers are white with border radius */
      .register-form div[phx-feedback-for],
      .register-card div[phx-feedback-for],
      .register-form .phx-form-error,
      .register-card .phx-form-error {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      /* Target any Phoenix wrapper divs and make them white */
      .register-form div,
      .register-card div {
        background-color: #ffffff !important;
        border-radius: inherit;
      }

      /* Specific Phoenix component wrappers */
      .register-form .phx-input,
      .register-card .phx-input,
      .register-form [data-phx-component],
      .register-card [data-phx-component] {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      /* Additional overrides for Tailwind classes */
      .register-form .bg-gray-800,
      .register-card .bg-gray-800 {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      .register-form .text-white,
      .register-card .text-white {
        color: #374151 !important;
      }

      /* Button Styling */
      .register-button {
        width: 100%;
        padding: 0.75rem 1.5rem;
        background: var(--secondary-gradient);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 0.95rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 0.5rem;
      }

      .register-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(249, 115, 22, 0.3);
      }

      .register-button:active {
        transform: translateY(0);
      }

      .register-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }

      /* Footer Links */
      .register-footer {
        text-align: center;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid rgba(100, 116, 139, 0.2);
      }

      .login-link {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }

      .login-link a {
        color: var(--probase-secondary);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .login-link a:hover {
        color: var(--probase-secondary);
        text-decoration: underline;
      }

      /* Error Styling */
      .error-message {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: #ef4444;
        padding: 1rem;
        border-radius: 12px;
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
      }

      /* Responsive Design */
      @media (max-width: 640px) {
        .register-container {
          padding: 1rem;
        }

        .register-card {
          padding: 2rem;
        }

        .register-title {
          font-size: 1.75rem;
        }
      }
    </style>

    <div class="register-bg"></div>

    <div class="register-container">
      <div class="register-card">
        <div class="register-header">
          <div class="register-logo">
            <img src="/images/probase-logo.png" alt="ProBASE Logo" />
            ProBASE
          </div>
          <h1 class="register-title">Create Account</h1>
          <p class="register-subtitle">Join thousands of finance professionals using ProBASE</p>
        </div>

        <.simple_form
          for={@form}
          id="registration_form"
          phx-submit="save"
          phx-change="validate"
          phx-trigger-action={@trigger_submit}
          action={~p"/users/log_in?_action=registered"}
          method="post"
          class="register-form"
        >
          <.error :if={@check_errors} class="error-message">
            Oops, something went wrong! Please check the errors below.
          </.error>

          <div class="form-group">
            <label for="user_email" class="form-label">Email Address</label>
            <.input field={@form[:email]} type="email" required class="form-input" placeholder="Enter your email" />
          </div>

          <div class="form-group">
            <label for="user_password" class="form-label">Password</label>
            <.input field={@form[:password]} type="password" required class="form-input" placeholder="Create a strong password" />
          </div>

          <:actions>
            <.button phx-disable-with="Creating account..." class="register-button">
              Create Account
            </.button>
          </:actions>
        </.simple_form>

        <div class="register-footer">
          <p class="login-link">
            Already have an account?
            <.link navigate={~p"/users/log_in"}>
              Sign in here
            </.link>
          </p>
        </div>
      </div>
    </div>
    """
  end

  def mount(_params, _session, socket) do
    changeset = Accounts.change_user_registration(%User{})

    socket =
      socket
      |> assign(trigger_submit: false, check_errors: false)
      |> assign(page_title: "Create Account")
      |> assign_form(changeset)

    {:ok, socket, temporary_assigns: [form: nil]}
  end

  def handle_event("save", %{"user" => user_params}, socket) do
    case Accounts.register_user(user_params) do
      {:ok, user} ->
        {:ok, _} =
          Accounts.deliver_user_confirmation_instructions(
            user,
            &url(~p"/users/confirm/#{&1}")
          )

        changeset = Accounts.change_user_registration(user)
        {:noreply, socket |> assign(trigger_submit: true) |> assign_form(changeset)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, socket |> assign(check_errors: true) |> assign_form(changeset)}
    end
  end

  def handle_event("validate", %{"user" => user_params}, socket) do
    changeset = Accounts.change_user_registration(%User{}, user_params)
    {:noreply, assign_form(socket, Map.put(changeset, :action, :validate))}
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    form = to_form(changeset, as: "user")

    if changeset.valid? do
      assign(socket, form: form, check_errors: false)
    else
      assign(socket, form: form)
    end
  end
end
