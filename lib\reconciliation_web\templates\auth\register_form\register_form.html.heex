<div class="register-bg"></div>

<div class="register-container">
  <div class="register-card">
    <div class="register-header">
      <div class="register-logo">
        <img src="/images/probase-logo.png" alt="ProBASE Logo" />
        ProBASE
      </div>
      <h1 class="register-title">Create Account</h1>
      <p class="register-subtitle">Join thousands of finance professionals using ProBASE</p>
    </div>

    <.simple_form
      for={@form}
      id="registration_form"
      phx-submit="save"
      phx-change="validate"
      phx-trigger-action={@trigger_submit}
      action={~p"/users/log_in?_action=registered"}
      method="post"
      class="register-form"
    >
      <.error :if={@check_errors} class="error-message">
        Oops, something went wrong! Please check the errors below.
      </.error>

      <div class="form-group">
        <label for="user_email" class="form-label">Email Address</label>
        <.input field={@form[:email]} type="email" required class="form-input" placeholder="Enter your email" />
      </div>

      <div class="form-group">
        <label for="user_password" class="form-label">Password</label>
        <.input field={@form[:password]} type="password" required class="form-input" placeholder="Create a strong password" />
      </div>

      <:actions>
        <.button phx-disable-with="Creating account..." class="register-button">
          Create Account
        </.button>
      </:actions>
    </.simple_form>

    <div class="register-footer">
      <p class="login-link">
        Already have an account?
        <.link navigate={~p"/users/log_in"}>
          Sign in here
        </.link>
      </p>
    </div>
  </div>
</div>
