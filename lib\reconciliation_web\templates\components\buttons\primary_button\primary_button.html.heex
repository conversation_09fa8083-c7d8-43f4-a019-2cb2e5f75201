<button 
  type={@type || "button"}
  class={[
    "inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-400 hover:bg-orange-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors duration-200",
    @class
  ]}
  {@rest}
>
  <%= if assigns[:icon] do %>
    <.icon name={@icon} class="w-4 h-4 mr-2" />
  <% end %>
  <%= render_slot(@inner_block) %>
</button>
