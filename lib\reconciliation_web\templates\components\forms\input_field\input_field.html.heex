<div class="form-group">
  <%= if assigns[:label] do %>
    <label for={@id} class="block text-sm font-medium text-gray-700 mb-1">
      <%= @label %>
      <%= if assigns[:required] do %>
        <span class="text-red-500">*</span>
      <% end %>
    </label>
  <% end %>
  
  <input
    type={@type || "text"}
    id={@id}
    name={@name}
    value={@value}
    placeholder={@placeholder}
    required={assigns[:required] || false}
    disabled={assigns[:disabled] || false}
    class={[
      "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm",
      (@errors != [] && "border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500") || "",
      @class
    ]}
    {@rest}
  />
  
  <%= if @errors != [] do %>
    <div class="mt-1">
      <%= for error <- @errors do %>
        <p class="text-sm text-red-600"><%= error %></p>
      <% end %>
    </div>
  <% end %>
  
  <%= if assigns[:help_text] do %>
    <p class="mt-1 text-sm text-gray-500"><%= @help_text %></p>
  <% end %>
</div>
