<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
    </div>
  </div>

  <!-- Main content -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <!-- Total Runs -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold">R</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Runs</dt>
                <dd class="text-lg font-medium text-gray-900"><%= format_number(@summary_data.total_runs) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Completed Runs -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold">✓</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                <dd class="text-lg font-medium text-gray-900"><%= format_number(@summary_data.completed_runs) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Transactions -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold">T</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Transactions</dt>
                <dd class="text-lg font-medium text-gray-900"><%= format_number(@summary_data.total_transactions) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Average Match Rate -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold">%</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Avg Match Rate</dt>
                <dd class="text-lg font-medium text-gray-900"><%= format_percentage(@summary_data.average_match_rate) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Runs -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Reconciliation Runs</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Your latest reconciliation activities</p>
      </div>
      
      <%= if Enum.empty?(@recent_runs) do %>
        <div class="px-4 py-5 sm:p-6">
          <div class="text-center">
            <p class="text-gray-500">No reconciliation runs yet.</p>
            <%= link "Start your first reconciliation", 
                to: ~p"/reconciliation/new", 
                class: "mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700" %>
          </div>
        </div>
      <% else %>
        <ul class="divide-y divide-gray-200">
          <%= for run <- @recent_runs do %>
            <li>
              <div class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-indigo-600 truncate">
                      <%= link run.name, to: ~p"/reconciliation/#{run.id}" %>
                    </p>
                    <span class={"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{status_badge_class(run.status)}"}>
                      <%= status_text(run.status) %>
                    </span>
                  </div>
                  <div class="ml-2 flex-shrink-0 flex">
                    <p class="text-sm text-gray-500">
                      <%= time_ago(run.inserted_at) %>
                    </p>
                  </div>
                </div>
                <%= if run.status == "completed" do %>
                  <div class="mt-2 sm:flex sm:justify-between">
                    <div class="sm:flex">
                      <p class="flex items-center text-sm text-gray-500">
                        Matched: <%= format_number(run.matched_count) %> transactions
                      </p>
                      <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                        Match Rate: <%= format_percentage(run.match_rate) %>
                      </p>
                    </div>
                  </div>
                <% end %>
              </div>
            </li>
          <% end %>
        </ul>
      <% end %>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8">
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
          <div class="mt-5 grid grid-cols-1 gap-4 sm:grid-cols-3">
            <%= link to: ~p"/reconciliation/new", class: "inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700" do %>
              New Reconciliation
            <% end %>
            
            <%= link to: ~p"/transactions", class: "inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" do %>
              View Transactions
            <% end %>
            
            <%= link to: ~p"/reports", class: "inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" do %>
              Generate Reports
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
