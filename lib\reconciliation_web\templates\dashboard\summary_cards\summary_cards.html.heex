<div class="max-w-7xl mx-auto p-6">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Reconciliation Summary</h1>
        <p class="text-gray-600">Comprehensive overview of your reconciliation activities and performance metrics</p>
        <%= if @selected_run_id do %>
          <% selected_run = Enum.find(@reconciliation_runs, &(&1.id == String.to_integer(@selected_run_id))) %>
          <%= if selected_run do %>
            <div class="mt-2 inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
              <.icon name="hero-funnel" class="w-4 h-4 mr-1" />
              Filtered by: <%= selected_run.name %>
            </div>
          <% end %>
        <% end %>
      </div>
      
      <!-- Run Filter -->
      <div class="flex items-center space-x-4">
        <!-- Specific Run Filter -->
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-white">Specific Run:</label>
          <form phx-change="filter_by_run" class="inline">
            <select
              name="run_id"
              class="border border-gray-600 rounded-lg px-3 py-2 text-sm bg-gray-800 text-white focus:ring-2 focus:ring-orange-500 focus:border-orange-500 min-w-[300px] max-w-[400px]"
            >
              <option value="" selected={@selected_run_id == nil}>
                <%= if length(@reconciliation_runs) == 0 do %>
                  No runs available
                <% else %>
                  All Runs (<%= length(@reconciliation_runs) %> available)
                <% end %>
              </option>
              <%= for run <- @reconciliation_runs do %>
                <option value={run.id} selected={@selected_run_id == to_string(run.id)}>
                  <%= run.name %> | <%= format_date(run.inserted_at) %> | <%= String.upcase(run.status) %>
                  <%= if run.match_rate do %>
                    | <%= Decimal.to_string(run.match_rate, :normal) %>% match
                  <% end %>
                </option>
              <% end %>
            </select>
          </form>
          <%= if length(@reconciliation_runs) >= 100 do %>
            <span class="text-xs text-gray-500">Showing recent 100 runs</span>
          <% end %>
        </div>

        <!-- Refresh Button -->
        <button
          phx-click="refresh_data"
          class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg text-sm font-medium flex items-center"
          title="Refresh data"
        >
          <.icon name="hero-arrow-path" class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>

  <%= if @loading do %>
    <!-- Loading State -->
    <div class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      <span class="ml-3 text-gray-600">Loading summary data...</span>
    </div>
  <% else %>
    <%= if has_data?(@summary_data) do %>
      <!-- Key Metrics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Files Uploaded -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <.icon name="hero-document-arrow-up" class="w-6 h-6 text-blue-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Files Uploaded</p>
              <p class="text-2xl font-bold text-gray-900"><%= format_number(@summary_data.file_upload_stats.total_files) %></p>
            </div>
          </div>
        </div>

        <!-- Total Transactions -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <.icon name="hero-banknotes" class="w-6 h-6 text-green-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Transactions</p>
              <p class="text-2xl font-bold text-gray-900"><%= format_number(@summary_data.transaction_metrics.total_entries_processed) %></p>
            </div>
          </div>
        </div>

        <!-- Match Rate -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
              <.icon name="hero-check-circle" class="w-6 h-6 text-emerald-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Overall Match Rate</p>
              <p class="text-2xl font-bold text-gray-900"><%= format_percentage(@summary_data.transaction_metrics.match_rate_percentage) %></p>
            </div>
          </div>
        </div>

        <!-- Completed Runs -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <.icon name="hero-chart-bar" class="w-6 h-6 text-purple-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Completed Runs</p>
              <p class="text-2xl font-bold text-gray-900"><%= format_number(@summary_data.summary_info.completed_runs) %></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Statistics Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- File Upload Statistics -->
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">File Upload Statistics</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Total Files</span>
                <span class="font-medium text-gray-900"><%= format_number(@summary_data.file_upload_stats.total_files) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Total Size</span>
                <span class="font-medium text-gray-900"><%= format_file_size(@summary_data.file_upload_stats.total_size) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Average File Size</span>
                <span class="font-medium text-gray-900"><%= format_file_size(@summary_data.file_upload_stats.avg_file_size) %></span>
              </div>
            </div>
            
            <!-- File Type Breakdown -->
            <%= if map_size(@summary_data.file_upload_stats.file_type_breakdown) > 0 do %>
              <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Uploaded Files Breakdown</h4>
                <div class="space-y-3">
                  <%= for {file_type, stats} <- @summary_data.file_upload_stats.file_type_breakdown do %>
                    <div class="border border-gray-200 rounded-lg p-3">
                      <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-600 font-medium"><%= file_type_display(file_type) %></span>
                        <span class="font-medium text-gray-900"><%= format_number(stats.count) %> files</span>
                      </div>
                      <%= if Map.has_key?(stats, :sample_names) and length(stats.sample_names) > 0 do %>
                        <div class="text-xs text-gray-500">
                          <span class="font-medium"></span>
                          <%= stats.sample_names |> Enum.map(&clean_file_name/1) |> Enum.join(", ") %>
                          <%= if stats.count > length(stats.sample_names) do %>
                            <span class="text-gray-400">and <%= stats.count - length(stats.sample_names) %> more...</span>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Transaction Matching Metrics -->
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Transaction Matching Metrics</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <!-- File Type Processing Counts -->
              <div class="bg-gray-50 rounded-lg p-4 mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-3">Processed by File Type</h4>
                <div class="grid grid-cols-2 gap-4">
                  <div class="flex justify-between items-center">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                      <div class="flex flex-col">
                        <span class="text-sm text-gray-600">
                          <%= if @summary_data.transaction_metrics.sample_file_a_name do %>
                            <%= @summary_data.transaction_metrics.sample_file_a_name %>
                          <% else %>
                            First Files (A)
                          <% end %>
                        </span>
                      </div>
                    </div>
                    <span class="font-medium text-blue-400"><%= format_number(@summary_data.transaction_metrics.total_processed_a) %></span>
                  </div>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <div class="flex flex-col">
                        <span class="text-sm text-gray-600">
                          <%= if @summary_data.transaction_metrics.sample_file_b_name do %>
                            <%= @summary_data.transaction_metrics.sample_file_b_name %>
                          <% else %>
                            Second Files (B)
                          <% end %>
                        </span>
                      </div>
                    </div>
                    <span class="font-medium text-green-400"><%= format_number(@summary_data.transaction_metrics.total_processed_b) %></span>
                  </div>
                </div>
              </div>

              <!-- Overall Metrics -->
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Total Processed</span>
                <span class="font-medium text-gray-900"><%= format_number(@summary_data.transaction_metrics.total_entries_processed) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Successfully Matched</span>
                <span class="font-medium text-green-400"><%= format_number(@summary_data.transaction_metrics.total_entries_matched) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Unmatched</span>
                <span class="font-medium text-red-400"><%= format_number(@summary_data.transaction_metrics.total_entries_unmatched) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Match Rate</span>
                <span class="font-medium text-gray-900"><%= format_percentage(@summary_data.transaction_metrics.match_rate_percentage) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Average Match Rate</span>
                <span class="font-medium text-gray-900"><%= format_percentage(@summary_data.transaction_metrics.avg_match_rate) %></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Currency Net Balance Comparison -->
      <%= if length(@transaction_totals.currency_net_balances) > 0 do %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Currency Reconciliation Analysis</h3>
            <p class="text-sm text-gray-600 mt-1">Detailed credit and debit breakdown by file and currency</p>
          </div>

          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                    Currency
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                    File A Credit
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                    File B Credit
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                    Credit Diff (A-B)
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                    File A Debit
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                    File B Debit
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Debit Diff (A-B)
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for currency_balance <- @transaction_totals.currency_net_balances do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-200">
                      <%= currency_balance.currency %>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-right border-r border-gray-200">
                      <span class="text-green-400">
                        <%= format_currency_with_symbol(currency_balance.file_a_credit, currency_balance.currency) %>
                      </span>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-right border-r border-gray-200">
                      <span class="text-green-400">
                        <%= format_currency_with_symbol(currency_balance.file_b_credit, currency_balance.currency) %>
                      </span>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-right border-r border-gray-200">
                      <span class={
                        cond do
                          Decimal.equal?(currency_balance.credit_diff, Decimal.new("0")) -> "text-green-400 font-semibold"
                          Decimal.negative?(currency_balance.credit_diff) -> "text-red-400"
                          true -> "text-blue-400"
                        end
                      }>
                        <%= format_currency_with_symbol(currency_balance.credit_diff, currency_balance.currency) %>
                      </span>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-right border-r border-gray-200">
                      <span class="text-red-400">
                        <%= format_currency_with_symbol(currency_balance.file_a_debit, currency_balance.currency) %>
                      </span>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-right border-r border-gray-200">
                      <span class="text-red-400">
                        <%= format_currency_with_symbol(currency_balance.file_b_debit, currency_balance.currency) %>
                      </span>
                    </td>
                    <td class="px-3 py-4 whitespace-nowrap text-sm text-right">
                      <span class={
                        cond do
                          Decimal.equal?(currency_balance.debit_diff, Decimal.new("0")) -> "text-green-400 font-semibold"
                          Decimal.negative?(currency_balance.debit_diff) -> "text-red-400"
                          true -> "text-blue-400"
                        end
                      }>
                        <%= format_currency_with_symbol(currency_balance.debit_diff, currency_balance.currency) %>
                      </span>
                    </td>
                  </tr>
                <% end %>


              </tbody>
            </table>
          </div>
        </div>
      <% end %>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
        <!-- Status Overview - COMMENTED OUT -->
        <%!-- <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Status Overview</h3>
          </div>
          <div class="p-6">
            <div class="space-y-3">
              <%= for {status, count} <- @summary_data.status_overview.status_breakdown do %>
                <div class="flex justify-between items-center">
                  <div class="flex items-center">
                    <span class={["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium", status_badge_class(status)]}>
                      <%= String.capitalize(status) %>
                    </span>
                  </div>
                  <span class="font-medium text-gray-900"><%= format_number(count) %></span>
                </div>
              <% end %>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Success Rate</span>
                <span class="font-medium text-gray-900"><%= format_percentage(@summary_data.status_overview.completion_rate) %></span>
              </div>
            </div>
          </div>
        </div> --%>







        <!-- Reconciliation Status Summary - COMMENTED OUT -->
        <%!-- <%= if length(@summary_data.currency_breakdown.currencies_found) > 0 do %>
          <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Reconciliation Status</h3>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Perfect Matches -->
                <div class="text-center p-4 bg-green-50 rounded-lg">
                  <div class="text-2xl font-bold text-green-400">
                    <%= @summary_data.currency_breakdown.currency_stats
                        |> Enum.count(fn {_currency, stats} ->
                          Decimal.equal?(stats.difference, Decimal.new("0"))
                        end) %>
                  </div>
                  <div class="text-sm text-green-700 font-medium">Perfect Matches</div>
                  <div class="text-xs text-green-400">No differences found</div>
                </div>

                <!-- Minor Differences -->
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                  <div class="text-2xl font-bold text-yellow-600">
                    <%= @summary_data.currency_breakdown.currency_stats
                        |> Enum.count(fn {_currency, stats} ->
                          not Decimal.equal?(stats.difference, Decimal.new("0")) and
                          Decimal.lt?(stats.difference_percentage, Decimal.new("5"))
                        end) %>
                  </div>
                  <div class="text-sm text-yellow-700 font-medium">Minor Differences</div>
                  <div class="text-xs text-yellow-600">&lt; 5% variance</div>
                </div>

                <!-- Major Differences -->
                <div class="text-center p-4 bg-red-50 rounded-lg">
                  <div class="text-2xl font-bold text-red-400">
                    <%= @summary_data.currency_breakdown.currency_stats
                        |> Enum.count(fn {_currency, stats} ->
                          Decimal.gte?(stats.difference_percentage, Decimal.new("5"))
                        end) %>
                  </div>
                  <div class="text-sm text-red-700 font-medium">Major Differences</div>
                  <div class="text-xs text-red-400">&ge; 5% variance</div>
                </div>
              </div>

              <!-- Overall Status -->
              <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="text-center">
                  <%= if Enum.all?(@summary_data.currency_breakdown.currency_stats, fn {_currency, stats} ->
                        Decimal.equal?(stats.difference, Decimal.new("0"))
                      end) do %>
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      <.icon name="hero-check-circle" class="w-4 h-4 mr-1" />
                      All Currencies Reconciled
                    </div>
                  <% else %>
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      <.icon name="hero-exclamation-triangle" class="w-4 h-4 mr-1" />
                      Differences Found - Review Required
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %> --%>

        <!-- Currency Reconciliation Summary - Full Width -->
      </div>

      <%!-- File Source & Currency Reconciliation (COMMENTED OUT) -->
      <%!-- <%= if map_size(@transaction_totals.file_source_totals) > 0 do %>
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden mb-8">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">File Source & Currency Reconciliation</h3>
          </div>
          <div class="overflow-x-auto">
            <%
              # Get all unique currencies from the file source currency totals
              # If no currencies are found, default to showing at least the currencies from the overall totals
              all_currencies = @transaction_totals.file_source_currency_totals
                |> Map.keys()
                |> Enum.map(fn {_file, currency} -> currency end)
                |> Enum.uniq()
                |> Enum.sort()

              # If no currencies found in file source breakdown, get them from overall currency totals
              all_currencies = if Enum.empty?(all_currencies) do
                @transaction_totals.currency_totals
                |> Map.keys()
                |> Enum.sort()
              else
                all_currencies
              end

              # If still no currencies, default to USD to ensure table structure is maintained
              all_currencies = if Enum.empty?(all_currencies), do: ["USD"], else: all_currencies

              # Debug: Log currency information (remove this after debugging)
              IO.inspect(all_currencies, label: "All currencies detected")
              IO.inspect(Map.keys(@transaction_totals.file_source_currency_totals), label: "File source currency keys")
            %>
            <table class="min-w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Source</th>
                  <%= for currency <- all_currencies do %>
                    <th class="px-4 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                      <%= currency %>
                      <div class="text-xs text-gray-400 normal-case">Debits | Credits</div>
                    </th>
                  <% end %>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-l-2 border-gray-300">Total Debits</th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Credits</th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Net Balance</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for {file_source, totals} <- @transaction_totals.file_source_totals do %>
                  <% net_balance = Decimal.sub(totals.credit_total, totals.debit_total) %>
                  <%
                    # Assign colors based on file order - first file gets blue, second gets green
                    file_index = @transaction_totals.file_source_totals |> Map.keys() |> Enum.find_index(&(&1 == file_source))
                    color_class = if file_index == 0, do: "bg-blue-500", else: "bg-green-500"
                  %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <span class={["w-4 h-4 rounded-full mr-3", color_class]}></span>
                        <div>
                          <div class="text-sm font-medium text-gray-900"><%= file_source %></div>
                        </div>
                      </div>
                    </td>
                    <%= for currency <- all_currencies do %>
                      <%
                        currency_totals = Map.get(@transaction_totals.file_source_currency_totals, {file_source, currency}, %{
                          debit_total: Decimal.new(0),
                          credit_total: Decimal.new(0),
                          debit_count: 0,
                          credit_count: 0
                        })
                      %>
                      <td class="px-4 py-4 whitespace-nowrap text-center border-l border-gray-200">
                        <div class="text-xs">
                          <div class="text-red-400">
                            <%= if Decimal.positive?(currency_totals.debit_total) do %>
                              <%= format_currency_with_symbol(currency_totals.debit_total, currency) %>
                            <% else %>
                              -
                            <% end %>
                          </div>
                          <div class="text-gray-400">|</div>
                          <div class="text-green-400">
                            <%= if Decimal.positive?(currency_totals.credit_total) do %>
                              <%= format_currency_with_symbol(currency_totals.credit_total, currency) %>
                            <% else %>
                              -
                            <% end %>
                          </div>
                        </div>
                      </td>
                    <% end %>
                    <td class="px-6 py-4 whitespace-nowrap text-right border-l-2 border-gray-300">
                      <div class="text-sm font-medium text-red-400"><%= format_currency(totals.debit_total) %></div>
                      <div class="text-xs text-gray-500"><%= format_number(totals.debit_count) %> transactions</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <div class="text-sm font-medium text-green-400"><%= format_currency(totals.credit_total) %></div>
                      <div class="text-xs text-gray-500"><%= format_number(totals.credit_count) %> transactions</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <div class={["text-sm font-medium",
                        if(Decimal.positive?(net_balance), do: "text-green-400", else: "text-red-400")]}><%= format_currency(net_balance) %></div>
                    </td>
                  </tr>
                <% end %>
              </tbody>
              <!-- Totals Footer -->
              <tfoot class="bg-gray-100 border-t-2 border-gray-300">
                <tr>
                  <td class="px-6 py-4 text-sm font-bold text-gray-900">TOTALS</td>
                  <%= for currency <- all_currencies do %>
                    <%
                      currency_debit_total = @transaction_totals.file_source_currency_totals
                        |> Enum.filter(fn {{_, curr}, _} -> curr == currency end)
                        |> Enum.map(fn {_, totals} -> totals.debit_total end)
                        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
                      currency_credit_total = @transaction_totals.file_source_currency_totals
                        |> Enum.filter(fn {{_, curr}, _} -> curr == currency end)
                        |> Enum.map(fn {_, totals} -> totals.credit_total end)
                        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
                    %>
                    <td class="px-4 py-4 text-center border-l border-gray-200">
                      <div class="text-xs font-semibold">
                        <div class="text-red-700">
                          <%= if Decimal.positive?(currency_debit_total) do %>
                            <%= format_currency_with_symbol(currency_debit_total, currency) %>
                          <% else %>
                            -
                          <% end %>
                        </div>
                        <div class="text-gray-400">|</div>
                        <div class="text-green-700">
                          <%= if Decimal.positive?(currency_credit_total) do %>
                            <%= format_currency_with_symbol(currency_credit_total, currency) %>
                          <% else %>
                            -
                          <% end %>
                        </div>
                      </div>
                    </td>
                  <% end %>
                  <td class="px-6 py-4 text-right border-l-2 border-gray-300">
                    <div class="text-sm font-bold text-red-700"><%= format_currency(@transaction_totals.total_debits) %></div>
                    <div class="text-xs text-gray-600">
                      <%= (@transaction_totals.file_source_totals |> Enum.map(fn {_, totals} -> totals.debit_count end) |> Enum.sum()) %> transactions
                    </div>
                  </td>
                  <td class="px-6 py-4 text-right">
                    <div class="text-sm font-bold text-green-700"><%= format_currency(@transaction_totals.total_credits) %></div>
                    <div class="text-xs text-gray-600">
                      <%= (@transaction_totals.file_source_totals |> Enum.map(fn {_, totals} -> totals.credit_count end) |> Enum.sum()) %> transactions
                    </div>
                  </td>
                  <td class="px-6 py-4 text-right">
                    <% net_difference = Decimal.sub(@transaction_totals.total_credits, @transaction_totals.total_debits) %>
                    <div class={["text-sm font-bold",
                      if(Decimal.positive?(net_difference), do: "text-green-700", else: "text-red-700")]}><%= format_currency(net_difference) %></div>
                  </td>
                </tr>
                <!-- Difference Row -->
                <%= if map_size(@transaction_totals.file_source_totals) == 2 do %>
                  <%
                    file_names = Map.keys(@transaction_totals.file_source_totals) |> Enum.sort()
                    [first_file, second_file] = file_names
                    first_file_totals = Map.get(@transaction_totals.file_source_totals, first_file, %{debit_total: Decimal.new(0), credit_total: Decimal.new(0)})
                    second_file_totals = Map.get(@transaction_totals.file_source_totals, second_file, %{debit_total: Decimal.new(0), credit_total: Decimal.new(0)})
                    debit_diff = Decimal.sub(first_file_totals.debit_total, second_file_totals.debit_total)
                    credit_diff = Decimal.sub(first_file_totals.credit_total, second_file_totals.credit_total)
                    net_diff = Decimal.sub(credit_diff, debit_diff)
                    # Create short names for the difference label
                    first_short = String.slice(first_file, 0, 8)
                    second_short = String.slice(second_file, 0, 8)
                  %>
                  <tr class="bg-yellow-50 border-t border-yellow-200">
                    <td class="px-6 py-4 text-sm font-semibold text-yellow-800">DIFFERENCE (<%= first_short %> - <%= second_short %>)</td>
                    <%= for currency <- all_currencies do %>
                      <%
                        first_currency_totals = Map.get(@transaction_totals.file_source_currency_totals, {first_file, currency}, %{debit_total: Decimal.new(0), credit_total: Decimal.new(0)})
                        second_currency_totals = Map.get(@transaction_totals.file_source_currency_totals, {second_file, currency}, %{debit_total: Decimal.new(0), credit_total: Decimal.new(0)})
                        currency_debit_diff = Decimal.sub(first_currency_totals.debit_total, second_currency_totals.debit_total)
                        currency_credit_diff = Decimal.sub(first_currency_totals.credit_total, second_currency_totals.credit_total)
                      %>
                      <td class="px-4 py-4 text-center border-l border-gray-200">
                        <div class="text-xs font-semibold">
                          <div class={["text-red-700", if(Decimal.equal?(currency_debit_diff, Decimal.new(0)), do: "text-gray-400")]}>
                            <%= if Decimal.equal?(currency_debit_diff, Decimal.new(0)) do %>
                              -
                            <% else %>
                              <%= format_currency_with_symbol(currency_debit_diff, currency) %>
                            <% end %>
                          </div>
                          <div class="text-gray-400">|</div>
                          <div class={["text-green-700", if(Decimal.equal?(currency_credit_diff, Decimal.new(0)), do: "text-gray-400")]}>
                            <%= if Decimal.equal?(currency_credit_diff, Decimal.new(0)) do %>
                              -
                            <% else %>
                              <%= format_currency_with_symbol(currency_credit_diff, currency) %>
                            <% end %>
                          </div>
                        </div>
                      </td>
                    <% end %>
                    <td class="px-6 py-4 text-right border-l-2 border-gray-300">
                      <div class={["text-sm font-semibold",
                        if(Decimal.positive?(debit_diff), do: "text-red-700", else: "text-green-700")]}><%= format_currency_with_sign(debit_diff) %></div>
                    </td>
                    <td class="px-6 py-4 text-right">
                      <div class={["text-sm font-semibold",
                        if(Decimal.positive?(credit_diff), do: "text-green-700", else: "text-red-700")]}><%= format_currency_with_sign(credit_diff) %></div>
                    </td>
                    <td class="px-6 py-4 text-right">
                      <div class={["text-sm font-semibold",
                        if(Decimal.positive?(net_diff), do: "text-green-700", else: "text-red-700")]}><%= format_currency_with_sign(net_diff) %></div>
                    </td>
                  </tr>
                <% end %>
              </tfoot>
            </table>
          </div>
        </div>
      <% end %> --%>



      <%!-- Currency Reconciliation Summary (COMMENTED OUT) -->
      <%!-- <%= if length(@summary_data.currency_breakdown.currencies_found) > 0 do %>
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden mb-8">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">Currency Reconciliation Summary</h3>
              <span class="text-sm text-gray-500"><%= length(@summary_data.currency_breakdown.currencies_found) %> currencies</span>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">File A</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">File B</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Difference</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for {currency, stats} <- @summary_data.currency_breakdown.currency_stats do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                          <span class="text-xs font-medium text-gray-600"><%= currency %></span>
                        </div>
                        <div class="ml-3">
                          <div class="text-sm font-medium text-gray-900"><%= currency %></div>
                          <div class="text-sm text-gray-500"><%= format_number(stats.transaction_count) %> transactions</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <div class="text-sm font-medium text-blue-700"><%= format_currency_with_symbol(stats.file_a_amount, currency) %></div>
                      <div class="text-sm text-gray-500"><%= format_number(stats.file_a_count) %> txns</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <div class="text-sm font-medium text-green-700"><%= format_currency_with_symbol(stats.file_b_amount, currency) %></div>
                      <div class="text-sm text-gray-500"><%= format_number(stats.file_b_count) %> txns</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <div class={["text-sm font-medium", difference_color(stats.difference)]}>
                        <%= format_difference_with_symbol(stats.difference, currency) %>
                      </div>
                      <%= if not Decimal.equal?(stats.difference_percentage, Decimal.new("0")) do %>
                        <div class={["text-xs", difference_color(stats.difference)]}>
                          <%= Decimal.to_string(stats.difference_percentage, :normal) %>%
                        </div>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <%= if Decimal.equal?(stats.difference, Decimal.new("0")) do %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                          </svg>
                          Matched
                        </span>
                      <% else %>
                        <% abs_percentage = Decimal.abs(stats.difference_percentage) %>
                        <%= if Decimal.lt?(abs_percentage, Decimal.new("5")) do %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            Minor
                          </span>
                        <% else %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            Major
                          </span>
                        <% end %>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          <%= if @summary_data.currency_breakdown.primary_currency do %>
            <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
              <div class="flex justify-between items-center text-sm">
                <span class="text-gray-600">Primary Currency</span>
                <span class="font-medium text-gray-900"><%= @summary_data.currency_breakdown.primary_currency %></span>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>

      <%!-- Remaining sections in grid -->
      <%!-- <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">

        <!-- Data Range - COMMENTED OUT -->
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Data Range</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div>
                <span class="text-sm text-gray-600">Period</span>
                <p class="font-medium text-gray-900">All Time</p>
              </div>
              <%= if @summary_data.date_range.earliest do %>
                <div>
                  <span class="text-sm text-gray-600">Earliest Record</span>
                  <p class="font-medium text-gray-900"><%= format_date(@summary_data.date_range.earliest) %></p>
                </div>
                <div>
                  <span class="text-sm text-gray-600">Latest Record</span>
                  <p class="font-medium text-gray-900"><%= format_date(@summary_data.date_range.latest) %></p>
                </div>
              <% else %>
                <div class="text-center py-4">
                  <p class="text-sm text-gray-500">No date range available</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div> --%>

      <!-- Quick Actions -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="flex flex-wrap gap-4">
          <.link
            navigate={~p"/reconciliation"}
            class="bg-orange-400 hover:bg-orange-500 text-white px-4 py-2 rounded-lg font-medium flex items-center"
          >
            <.icon name="hero-plus" class="w-4 h-4 mr-2" />
            New Reconciliation
          </.link>
          <.link
            navigate={~p"/reports"}
            class="bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-medium flex items-center"
          >
            <.icon name="hero-chart-bar" class="w-4 h-4 mr-2" />
            View Reports
          </.link>
          <.link
            navigate={~p"/transactions"}
            class="bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-medium flex items-center"
          >
            <.icon name="hero-banknotes" class="w-4 h-4 mr-2" />
            View Transactions
          </.link>
        </div>
      </div>

    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <.icon name="hero-chart-bar" class="w-12 h-12 text-gray-400" />
        </div>
        <h3 class="text-xl font-medium text-gray-900 mb-2">No Reconciliation Data</h3>
        <p class="text-gray-500 mb-8 max-w-md mx-auto">
          You haven't created any reconciliation runs yet. Start by uploading your first set of files to see comprehensive summary statistics here.
        </p>
        <.link
          navigate={~p"/reconciliation"}
          class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center"
        >
          <.icon name="hero-plus" class="w-5 h-5 mr-2" />
          Create Your First Reconciliation
        </.link>
      </div>
    <% end %>
  <% end %>
</div>
