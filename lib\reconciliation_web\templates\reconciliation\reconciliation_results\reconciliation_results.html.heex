<div class="max-w-7xl mx-auto p-6">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900"><%= @reconciliation_run.name %></h1>
        <div class="flex items-center mt-2">
          <.icon name={status_icon(@reconciliation_run.status)} class={"w-5 h-5 mr-2 text-#{status_color(@reconciliation_run.status)}-600"} />
          <span class={"text-#{status_color(@reconciliation_run.status)}-600 font-medium capitalize"}>
            <%= @reconciliation_run.status %>
          </span>
          <%= if @reconciliation_run.processed_at do %>
            <span class="text-gray-500 ml-4">
              Completed <%= Calendar.strftime(@reconciliation_run.processed_at, "%B %d, %Y at %I:%M %p") %>
            </span>
          <% end %>
        </div>
      </div>
      <div class="flex space-x-3">
        <.link navigate={~p"/reconciliation"} class="btn btn-outline">
          <.icon name="hero-arrow-left" class="w-4 h-4 mr-2" />
          Back to List
        </.link>
        <.link navigate={~p"/reconciliation/#{@reconciliation_run.id}/proof_reading"} class="btn btn-outline">
          <.icon name="hero-document-magnifying-glass" class="w-4 h-4 mr-2" />
          Proof Reading
        </.link>
        <.link navigate={~p"/reconciliation/#{@reconciliation_run.id}/inspect"} class="btn btn-outline">
          <.icon name="hero-eye" class="w-4 h-4 mr-2" />
          Inspect Data
        </.link>
        <.button class="btn btn-primary">
          <.icon name="hero-arrow-down-tray" class="w-4 h-4 mr-2" />
          Export Report
        </.button>
      </div>
    </div>
  </div>

  <%= if @reconciliation_run.status == "processing" do %>
    <!-- Processing Status -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
      <div class="flex items-center">
        <.icon name="hero-clock" class="w-6 h-6 text-yellow-600 mr-3 animate-spin" />
        <div>
          <h3 class="text-lg font-medium text-yellow-800">Processing Reconciliation</h3>
          <p class="text-yellow-700">Please wait while we analyze your files and match transactions...</p>
        </div>
      </div>
    </div>
  <% end %>

  <%= if @reconciliation_run.status == "failed" do %>
    <!-- Error Status -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
      <div class="flex items-center">
        <.icon name="hero-x-circle" class="w-6 h-6 text-red-600 mr-3" />
        <div>
          <h3 class="text-lg font-medium text-red-800">Processing Failed</h3>
          <p class="text-red-700"><%= @reconciliation_run.error_message %></p>
        </div>
      </div>
    </div>
  <% end %>

  <%= if @reconciliation_run.status == "completed" do %>
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <!-- Total Processed A -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
              <span class="text-white font-semibold text-xs">A</span>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Processed A</p>
            <p class="text-2xl font-bold text-blue-600">
              <%= @stats.total_transactions_a %>
            </p>
          </div>
        </div>
      </div>

      <!-- Total Processed B -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
              <span class="text-white font-semibold text-xs">B</span>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Processed B</p>
            <p class="text-2xl font-bold text-green-600">
              <%= @stats.total_transactions_b %>
            </p>
          </div>
        </div>
      </div>

      <!-- Combined Total -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
            <.icon name="hero-document-text" class="w-6 h-6 text-gray-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Transactions</p>
            <p class="text-2xl font-bold text-gray-900">
              <%= @stats.total_transactions_a + @stats.total_transactions_b %>
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <.icon name="hero-check-circle" class="w-6 h-6 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Match Rate</p>
            <p class="text-2xl font-bold text-gray-900">
              <%= format_percentage(@stats.match_rate) %>
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
            <.icon name="hero-exclamation-triangle" class="w-6 h-6 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Unmatched</p>
            <p class="text-2xl font-bold text-gray-900">
              <%= @stats.unmatched_a_count + @stats.unmatched_b_count %>
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <.icon name="hero-banknotes" class="w-6 h-6 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Amount Difference</p>
            <p class="text-2xl font-bold text-gray-900">
              <%= if @stats.primary_currency do %>
                <%= format_currency_with_symbol(@stats.difference_amount, @stats.primary_currency) %>
              <% else %>
                <%= format_currency(@stats.difference_amount) %>
              <% end %>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-lg shadow-sm border mb-8">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6">
          <button
            phx-click="change_tab"
            phx-value-tab="overview"
            class={[
              "py-4 px-1 border-b-2 font-medium text-sm",
              if(@selected_tab == "overview", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
            ]}
          >
            Overview
          </button>
          <button
            phx-click="change_tab"
            phx-value-tab="matched"
            class={[
              "py-4 px-1 border-b-2 font-medium text-sm",
              if(@selected_tab == "matched", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
            ]}
          >
            Matched Transactions (<%= @stats.matched_count %>)
          </button>
          <button
            phx-click="change_tab"
            phx-value-tab="unmatched"
            class={[
              "py-4 px-1 border-b-2 font-medium text-sm",
              if(@selected_tab == "unmatched", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
            ]}
          >
            Unmatched Transactions (<%= @stats.unmatched_a_count + @stats.unmatched_b_count %>)
          </button>
        </nav>
      </div>

      <div class="p-6">
        <%= case @selected_tab do %>
          <% "overview" -> %>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- File A Summary -->
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <span class="text-blue-600 font-semibold text-sm">A</span>
                  </div>
                  <div class="flex-1">
                    <div><%= Reconciliation.ReconciliationRun.file_a_display_name(@reconciliation_run) %></div>
                    <div class="text-sm text-gray-500 font-normal">Summary</div>
                  </div>
                </h3>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">Total Transactions:</span>
                    <span class="font-medium"><%= @stats.total_transactions_a %></span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Total Amount:</span>
                    <span class="font-medium">
                      <%= if @stats.primary_currency do %>
                        <%= format_currency_with_symbol(@stats.total_amount_a, @stats.primary_currency) %>
                      <% else %>
                        <%= format_currency(@stats.total_amount_a) %>
                      <% end %>
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Matched:</span>
                    <span class="font-medium text-green-600"><%= @stats.matched_count %></span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Unmatched:</span>
                    <span class="font-medium text-yellow-600"><%= @stats.unmatched_a_count %></span>
                  </div>
                </div>
              </div>

              <!-- File B Summary -->
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                    <span class="text-green-600 font-semibold text-sm">B</span>
                  </div>
                  <div class="flex-1">
                    <div><%= Reconciliation.ReconciliationRun.file_b_display_name(@reconciliation_run) %></div>
                    <div class="text-sm text-gray-500 font-normal">Summary</div>
                  </div>
                </h3>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">Total Transactions:</span>
                    <span class="font-medium"><%= @stats.total_transactions_b %></span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Total Amount:</span>
                    <span class="font-medium">
                      <%= if @stats.primary_currency do %>
                        <%= format_currency_with_symbol(@stats.total_amount_b, @stats.primary_currency) %>
                      <% else %>
                        <%= format_currency(@stats.total_amount_b) %>
                      <% end %>
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Matched:</span>
                    <span class="font-medium text-green-600"><%= @stats.matched_count %></span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Unmatched:</span>
                    <span class="font-medium text-yellow-600"><%= @stats.unmatched_b_count %></span>
                  </div>
                </div>
              </div>
            </div>

          <% "matched" -> %>
            <% grouped = group_transactions_by_match_status(@transactions, @matches) %>
            <%= if Enum.any?(grouped.matched_pairs) do %>
              <div class="space-y-4">
                <%= for pair <- grouped.matched_pairs do %>
                  <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center">
                        <span class={[
                          "px-2 py-1 rounded-full text-xs font-medium",
                          "bg-#{confidence_color(Decimal.to_float(pair.match.confidence_score))}-100",
                          "text-#{confidence_color(Decimal.to_float(pair.match.confidence_score))}-800"
                        ]}>
                          <%= Decimal.to_string(pair.match.confidence_score, :normal) %>% Match
                        </span>
                        <span class="ml-2 text-sm text-gray-500 capitalize">
                          <%= pair.match.match_type %> Match
                        </span>
                      </div>
                      <%= unless pair.match.verified_by_user do %>
                        <button
                          phx-click="verify_match"
                          phx-value-match_id={pair.match.id}
                          class="text-sm text-indigo-600 hover:text-indigo-800"
                        >
                          Verify Match
                        </button>
                      <% else %>
                        <span class="text-sm text-green-600 flex items-center">
                          <.icon name="hero-check-circle" class="w-4 h-4 mr-1" />
                          Verified
                        </span>
                      <% end %>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <!-- Transaction A -->
                      <div class="bg-blue-50 rounded p-3">
                        <div class="flex items-center justify-between mb-2">
                          <div class="flex items-center">
                            <div class="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                              <span class="text-blue-600 font-semibold text-xs">A</span>
                            </div>
                            <span class="font-medium text-gray-900"><%= Reconciliation.ReconciliationRun.file_a_short_name(@reconciliation_run) %></span>
                          </div>
                          <div class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded" title={Reconciliation.UploadedFile.display_name(pair.transaction_a.uploaded_file)}>
                            <%= Reconciliation.UploadedFile.short_display_name(pair.transaction_a.uploaded_file, 20) %>
                          </div>
                        </div>
                        <div class="text-sm space-y-1">
                          <div><strong>Amount:</strong>
                            <%= if pair.transaction_a.currency do %>
                              <%= format_currency_with_symbol(pair.transaction_a.amount, pair.transaction_a.currency) %>
                            <% else %>
                              <%= format_currency(pair.transaction_a.amount) %>
                            <% end %>
                          </div>
                          <%= if pair.transaction_a.transaction_date do %>
                            <div><strong>Date:</strong> <%= pair.transaction_a.transaction_date %></div>
                          <% end %>
                          <%= if pair.transaction_a.reference do %>
                            <div><strong>Reference:</strong> <%= pair.transaction_a.reference %></div>
                          <% end %>
                          <%= if pair.transaction_a.description do %>
                            <div><strong>Description:</strong> <%= pair.transaction_a.description %></div>
                          <% end %>
                        </div>
                      </div>

                      <!-- Transaction B -->
                      <div class="bg-green-50 rounded p-3">
                        <div class="flex items-center justify-between mb-2">
                          <div class="flex items-center">
                            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-2">
                              <span class="text-green-600 font-semibold text-xs">B</span>
                            </div>
                            <span class="font-medium text-gray-900"><%= Reconciliation.ReconciliationRun.file_b_short_name(@reconciliation_run) %></span>
                          </div>
                          <div class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded" title={Reconciliation.UploadedFile.display_name(pair.transaction_b.uploaded_file)}>
                            <%= Reconciliation.UploadedFile.short_display_name(pair.transaction_b.uploaded_file, 20) %>
                          </div>
                        </div>
                        <div class="text-sm space-y-1">
                          <div><strong>Amount:</strong>
                            <%= if pair.transaction_b.currency do %>
                              <%= format_currency_with_symbol(pair.transaction_b.amount, pair.transaction_b.currency) %>
                            <% else %>
                              <%= format_currency(pair.transaction_b.amount) %>
                            <% end %>
                          </div>
                          <%= if pair.transaction_b.transaction_date do %>
                            <div><strong>Date:</strong> <%= pair.transaction_b.transaction_date %></div>
                          <% end %>
                          <%= if pair.transaction_b.reference do %>
                            <div><strong>Reference:</strong> <%= pair.transaction_b.reference %></div>
                          <% end %>
                          <%= if pair.transaction_b.description do %>
                            <div><strong>Description:</strong> <%= pair.transaction_b.description %></div>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-8">
                <.icon name="hero-check-circle" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-500">No matched transactions found</p>
              </div>
            <% end %>

          <% "unmatched" -> %>
            <% grouped = group_transactions_by_match_status(@transactions, @matches) %>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Unmatched File A -->
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <span class="text-blue-600 font-semibold text-sm">A</span>
                  </div>
                  <div class="flex-1">
                    <div>Unmatched in <%= Reconciliation.ReconciliationRun.file_a_short_name(@reconciliation_run) %></div>
                    <div class="text-sm text-gray-500 font-normal">(<%= length(grouped.unmatched_a) %> transactions)</div>
                  </div>
                </h3>
                <%= if Enum.any?(grouped.unmatched_a) do %>
                  <div class="space-y-3">
                    <%= for transaction <- grouped.unmatched_a do %>
                      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex justify-between items-start mb-2">
                          <div class="text-sm font-medium">
                            <%= if transaction.currency do %>
                              <%= format_currency_with_symbol(transaction.amount, transaction.currency) %>
                            <% else %>
                              <%= format_currency(transaction.amount) %>
                            <% end %>
                          </div>
                          <div class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded" title={Reconciliation.UploadedFile.display_name(transaction.uploaded_file)}>
                            <%= Reconciliation.UploadedFile.short_display_name(transaction.uploaded_file, 25) %>
                          </div>
                        </div>
                        <div class="text-sm space-y-1">
                          <%= if transaction.transaction_date do %>
                            <div class="text-gray-600"><%= transaction.transaction_date %></div>
                          <% end %>
                          <%= if transaction.description do %>
                            <div class="text-gray-600"><%= transaction.description %></div>
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                <% else %>
                  <p class="text-gray-500 text-center py-4">All transactions matched</p>
                <% end %>
              </div>

              <!-- Unmatched File B -->
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                    <span class="text-green-600 font-semibold text-sm">B</span>
                  </div>
                  <div class="flex-1">
                    <div>Unmatched in <%= Reconciliation.ReconciliationRun.file_b_short_name(@reconciliation_run) %></div>
                    <div class="text-sm text-gray-500 font-normal">(<%= length(grouped.unmatched_b) %> transactions)</div>
                  </div>
                </h3>
                <%= if Enum.any?(grouped.unmatched_b) do %>
                  <div class="space-y-3">
                    <%= for transaction <- grouped.unmatched_b do %>
                      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex justify-between items-start mb-2">
                          <div class="text-sm font-medium">
                            <%= if transaction.currency do %>
                              <%= format_currency_with_symbol(transaction.amount, transaction.currency) %>
                            <% else %>
                              <%= format_currency(transaction.amount) %>
                            <% end %>
                          </div>
                          <div class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded" title={Reconciliation.UploadedFile.display_name(transaction.uploaded_file)}>
                            <%= Reconciliation.UploadedFile.short_display_name(transaction.uploaded_file, 25) %>
                          </div>
                        </div>
                        <div class="text-sm space-y-1">
                          <%= if transaction.transaction_date do %>
                            <div class="text-gray-600"><%= transaction.transaction_date %></div>
                          <% end %>
                          <%= if transaction.description do %>
                            <div class="text-gray-600"><%= transaction.description %></div>
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                <% else %>
                  <p class="text-gray-500 text-center py-4">All transactions matched</p>
                <% end %>
              </div>
            </div>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
