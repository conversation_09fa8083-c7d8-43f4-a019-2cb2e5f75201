<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Sign in to ProBASE
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Financial Reconciliation Platform
      </p>
    </div>
    
    <.form for={%{}} action={~p"/users/log_in"} method="post" class="mt-8 space-y-6">
      <input type="hidden" name="_action" value="login" />

      <!-- Display error message if login failed -->
      <div :if={Phoenix.Flash.get(@flash, :error)} class="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-800 font-medium">
              <%= Phoenix.Flash.get(@flash, :error) %>
            </p>
          </div>
        </div>
      </div>

      <div class="rounded-md shadow-sm -space-y-px">
        <div>
          <label for="user_email" class="sr-only">Email address</label>
          <input
            id="user_email"
            name="user[email]"
            type="email"
            autocomplete="email"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Email address"
          />
        </div>
        <div>
          <label for="user_password" class="sr-only">Password</label>
          <input
            id="user_password"
            name="user[password]"
            type="password"
            autocomplete="current-password"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Password"
          />
        </div>
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input
            id="user_remember_me"
            name="user[remember_me]"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="user_remember_me" class="ml-2 block text-sm text-gray-900">
            Remember me
          </label>
        </div>

        <div class="text-sm">
          <.link href={~p"/users/reset_password"} class="font-medium text-indigo-600 hover:text-indigo-500">
            Forgot password?
          </.link>
        </div>
      </div>

      <div>
        <button
          type="submit"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Sign in
        </button>
      </div>

      <div class="text-center">
        <span class="text-sm text-gray-600">Don't have an account?</span>
        <.link href={~p"/users/register"} class="font-medium text-indigo-600 hover:text-indigo-500">
          Register here
        </.link>
      </div>
    </.form>
  </div>
</div>
