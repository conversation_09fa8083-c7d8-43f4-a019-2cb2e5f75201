defmodule Reconciliation.Services.ActivityLogger do
  @moduledoc """
  Service for logging user activities across the application.
  Provides a centralized way to track user actions for audit trails and analytics.
  """

  alias Reconciliation.Repo
  alias Reconciliation.Accounts.UserActivityLog
  require Logger

  @doc """
  Log a user activity with comprehensive context information.
  
  ## Parameters
  - `user_id`: ID of the user performing the action (can be nil for anonymous actions)
  - `activity_type`: Type of activity (authentication, reconciliation, system, data_access, user_management, security)
  - `action`: Specific action performed
  - `opts`: Additional options including:
    - `:resource_type` - Type of resource being acted upon
    - `:resource_id` - ID of the resource
    - `:session_id` - Current session ID
    - `:ip_address` - User's IP address
    - `:user_agent` - User's browser/client information
    - `:request_path` - HTTP request path
    - `:request_method` - HTTP request method
    - `:response_status` - HTTP response status
    - `:duration_ms` - Request duration in milliseconds
    - `:metadata` - Additional metadata as a map
    - `:organization_id` - Organization context
  
  ## Examples
  
      # Log a login attempt
      ActivityLogger.log_activity(user.id, "authentication", "login", 
        session_id: session_id,
        ip_address: "***********",
        user_agent: "Mozilla/5.0...",
        response_status: 200
      )
      
      # Log a reconciliation upload
      ActivityLogger.log_activity(user.id, "reconciliation", "file_upload",
        resource_type: "uploaded_file",
        resource_id: file.id,
        metadata: %{filename: file.original_filename, size: file.file_size}
      )
  """
  def log_activity(user_id, activity_type, action, opts \\ []) do
    attrs = %{
      user_id: user_id,
      activity_type: activity_type,
      action: action,
      session_id: Keyword.get(opts, :session_id),
      resource_type: Keyword.get(opts, :resource_type),
      resource_id: Keyword.get(opts, :resource_id),
      ip_address: Keyword.get(opts, :ip_address),
      user_agent: Keyword.get(opts, :user_agent),
      request_path: Keyword.get(opts, :request_path),
      request_method: Keyword.get(opts, :request_method),
      response_status: Keyword.get(opts, :response_status),
      duration_ms: Keyword.get(opts, :duration_ms),
      metadata: Keyword.get(opts, :metadata, %{}),
      organization_id: Keyword.get(opts, :organization_id)
    }

    case create_activity_log(attrs) do
      {:ok, log} ->
        {:ok, log}
      {:error, changeset} ->
        Logger.error("Failed to log activity: #{inspect(changeset.errors)}")
        {:error, changeset}
    end
  end

  @doc """
  Log authentication activities (login, logout, password changes, etc.)
  """
  def log_auth_activity(user_id, action, opts \\ []) do
    log_activity(user_id, "authentication", action, opts)
  end

  @doc """
  Log reconciliation activities (uploads, runs, exports, etc.)
  """
  def log_reconciliation_activity(user_id, action, opts \\ []) do
    log_activity(user_id, "reconciliation", action, opts)
  end

  @doc """
  Log system activities (settings changes, user management, etc.)
  """
  def log_system_activity(user_id, action, opts \\ []) do
    log_activity(user_id, "system", action, opts)
  end

  @doc """
  Log data access activities (views, searches, exports, etc.)
  """
  def log_data_access_activity(user_id, action, opts \\ []) do
    log_activity(user_id, "data_access", action, opts)
  end

  @doc """
  Log user management activities (profile updates, role changes, etc.)
  """
  def log_user_management_activity(user_id, action, opts \\ []) do
    log_activity(user_id, "user_management", action, opts)
  end

  @doc """
  Log security activities (permission changes, suspicious activities, etc.)
  """
  def log_security_activity(user_id, action, opts \\ []) do
    log_activity(user_id, "security", action, opts)
  end

  @doc """
  Log activity from Phoenix connection context.
  Automatically extracts IP address, user agent, request path, etc.
  """
  def log_activity_from_conn(conn, user_id, activity_type, action, opts \\ []) do
    conn_opts = [
      ip_address: get_ip_address(conn),
      user_agent: get_user_agent(conn),
      request_path: conn.request_path,
      request_method: conn.method,
      session_id: get_session_id(conn)
    ]

    merged_opts = Keyword.merge(conn_opts, opts)
    log_activity(user_id, activity_type, action, merged_opts)
  end

  @doc """
  Log activity with timing information.
  Useful for tracking performance of operations.
  """
  def log_timed_activity(user_id, activity_type, action, start_time, opts \\ []) do
    duration_ms = System.monotonic_time(:millisecond) - start_time
    opts_with_duration = Keyword.put(opts, :duration_ms, duration_ms)
    log_activity(user_id, activity_type, action, opts_with_duration)
  end

  @doc """
  Batch log multiple activities.
  Useful for logging multiple related actions efficiently.
  """
  def log_activities(activities) when is_list(activities) do
    activity_logs = Enum.map(activities, fn {user_id, activity_type, action, opts} ->
      %{
        user_id: user_id,
        activity_type: activity_type,
        action: action,
        session_id: Keyword.get(opts, :session_id),
        resource_type: Keyword.get(opts, :resource_type),
        resource_id: Keyword.get(opts, :resource_id),
        ip_address: Keyword.get(opts, :ip_address),
        user_agent: Keyword.get(opts, :user_agent),
        request_path: Keyword.get(opts, :request_path),
        request_method: Keyword.get(opts, :request_method),
        response_status: Keyword.get(opts, :response_status),
        duration_ms: Keyword.get(opts, :duration_ms),
        metadata: Keyword.get(opts, :metadata, %{}),
        organization_id: Keyword.get(opts, :organization_id),
        inserted_at: DateTime.utc_now() |> DateTime.truncate(:second)
      }
    end)

    case Repo.insert_all(UserActivityLog, activity_logs) do
      {count, _} when count > 0 ->
        {:ok, count}
      _ ->
        {:error, "Failed to insert activity logs"}
    end
  end

  @doc """
  Get recent activities for a user
  """
  def get_user_activities(user_id, limit \\ 50) do
    UserActivityLog
    |> UserActivityLog.by_user(user_id)
    |> UserActivityLog.recent(limit)
    |> UserActivityLog.with_associations()
    |> Repo.all()
  end

  @doc """
  Get activities for an organization
  """
  def get_organization_activities(organization_id, limit \\ 100) do
    UserActivityLog
    |> UserActivityLog.by_organization(organization_id)
    |> UserActivityLog.recent(limit)
    |> UserActivityLog.with_associations()
    |> Repo.all()
  end

  @doc """
  Get suspicious activities
  """
  def get_suspicious_activities(limit \\ 50) do
    UserActivityLog
    |> UserActivityLog.suspicious_activities()
    |> UserActivityLog.recent(limit)
    |> UserActivityLog.with_associations()
    |> Repo.all()
  end

  @doc """
  Get activity summary for a user within a date range
  """
  def get_activity_summary(user_id, start_date, end_date) do
    UserActivityLog.activity_summary(user_id, start_date, end_date)
    |> Repo.all()
  end

  # Private functions

  defp create_activity_log(attrs) do
    %UserActivityLog{}
    |> UserActivityLog.changeset(attrs)
    |> Repo.insert()
  end

  defp get_ip_address(conn) do
    case Plug.Conn.get_req_header(conn, "x-forwarded-for") do
      [ip | _] -> ip |> String.split(",") |> List.first() |> String.trim()
      [] -> conn.remote_ip |> :inet.ntoa() |> to_string()
    end
  end

  defp get_user_agent(conn) do
    case Plug.Conn.get_req_header(conn, "user-agent") do
      [user_agent | _] -> user_agent
      [] -> nil
    end
  end

  defp get_session_id(conn) do
    Plug.Conn.get_session(conn, :live_socket_id) ||
    Plug.Conn.get_session(conn, :user_token)
  end
end
