defmodule Reconciliation.Services.ExcelExport do
  @moduledoc """
  Service for exporting data to Excel (.xlsx) format using Excelizer only.
  Provides reliable Excel exports with template preservation.
  
  All UmyaSpreadsheet functionality has been removed due to API compatibility issues.
  This service now delegates all Excel operations to ExcelizerExport.
  """

  alias Reconciliation.Services.ExcelizerExport
  require Logger

  @doc """
  Generate Excel content using Excelizer only.
  All UmyaSpreadsheet functionality has been removed due to API issues.
  
  ## Parameters
  - `transactions`: List of transaction structs to export
  - `run_name`: Name for the export file (optional, defaults to "export")
  
  ## Returns
  - `{:ok, file_path, filename}` on success
  - `{:error, reason}` on failure
  """
  def generate_transactions_excel_from_template(transactions, run_name \\ "export") do
    Logger.info("Starting Excel export using Excelizer for #{length(transactions)} transactions")
    ExcelizerExport.generate_transactions_excel_from_template(transactions, run_name)
  end

  @doc """
  Generate reconciliation report Excel using Excelizer only.
  
  ## Parameters
  - `run`: Reconciliation run struct
  - `transactions`: List of transaction structs
  - `matches`: List of transaction match structs
  
  ## Returns
  - `{:ok, base64_data}` on success
  - `{:error, reason}` on failure
  """
  def generate_reconciliation_report_excel(run, transactions, matches) do
    Logger.info("Starting reconciliation report export using Excelizer")
    ExcelizerExport.generate_reconciliation_report_excel(run, transactions, matches)
  end
end
