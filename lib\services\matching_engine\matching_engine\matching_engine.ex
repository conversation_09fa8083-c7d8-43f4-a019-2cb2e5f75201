
defmodule Reconciliation.Services.MatchingEngine do
  @moduledoc """
  Service for matching transactions between two files in a reconciliation run.
  """

  require Logger

  alias Reconciliation.{
    Repo,
    Transaction,
    TransactionMatch,
    ReconciliationRun
  }

  @doc """
  Performs transaction matching for a reconciliation run.
  """
  def match_transactions(%ReconciliationRun{} = run) do
    Logger.info("Starting transaction matching for run #{run.id}")

    # Get user settings
    {:ok, settings} = Reconciliation.get_or_create_settings(run.user_id)

    # Get transactions from both files
    transactions = Reconciliation.get_transactions(run.id)

    # Ensure we have transactions from both files
    file_a_transactions = Enum.filter(transactions, fn transaction ->
      transaction.uploaded_file.file_type == "file_a"
    end)

    file_b_transactions = Enum.filter(transactions, fn transaction ->
      transaction.uploaded_file.file_type == "file_b"
    end)

    Logger.info("Found #{length(file_a_transactions)} transactions in File A, #{length(file_b_transactions)} in File B")
    Logger.info("Total transactions to process: #{length(transactions)}")

    # Initialize all transactions with unmatched status at the start
    # This ensures every transaction gets a status, even if matching fails
    Logger.info("Pre-initializing all transaction match statuses")
    Enum.each(transactions, fn transaction ->
      try do
        Reconciliation.update_transaction_match_status(transaction, %{
          is_matched: false,
          match_confidence: nil
        })
      rescue
        error ->
          Logger.warn("Error pre-initializing transaction #{transaction.id}: #{inspect(error)}")
      end
    end)

    # Perform matching
    matches = find_exact_matches_between_files(file_a_transactions, file_b_transactions, settings)

    # Create match records and update transaction statuses
    save_matches_to_database(matches, run.id)

    # Update reconciliation run statistics
    Reconciliation.calculate_reconciliation_stats(run.id)

    Logger.info("Completed transaction matching for run #{run.id}")
    {:ok, matches}
  end

  # Find exact matches between transactions from file A and file B
  defp find_exact_matches_between_files(file_a_transactions, file_b_transactions, settings) do
    Logger.info("Finding exact matches only")

    if settings.auto_match_exact do
      {exact_matches, _remaining_a, _remaining_b} = compare_all_transaction_pairs(file_a_transactions, file_b_transactions, settings)
      Logger.info("Total exact matches found: #{length(exact_matches)}")
      exact_matches
    else
      Logger.info("Auto exact matching is disabled")
      []
    end
  end

  # Compare all transaction pairs to find exact matches
  defp compare_all_transaction_pairs(file_a_transactions, file_b_transactions, settings) do
    Logger.info("Comparing transactions: #{length(file_a_transactions)} from File A vs #{length(file_b_transactions)} from File B")

    matches = for first_transaction <- file_a_transactions,
                  second_transaction <- file_b_transactions do
      case transactions_match_exactly?(first_transaction, second_transaction, settings) do
        {:match, criteria} ->
          Logger.debug("Found exact match: A(#{first_transaction.id}) <-> B(#{second_transaction.id}) - Criteria: #{inspect(criteria)}")
          %{
            transaction_a: first_transaction,
            transaction_b: second_transaction,
            match_type: "exact",
            confidence_score: Decimal.new("100"),
            matching_criteria: criteria
          }
        {:no_match, reason} ->
          Logger.debug("No match A(#{first_transaction.id}) <-> B(#{second_transaction.id}): #{reason}")
          nil
        {:error, error} ->
          Logger.warn("Error matching A(#{first_transaction.id}) <-> B(#{second_transaction.id}): #{error}")
          nil
      end
    end
    |> Enum.reject(&is_nil/1)

    Logger.info("Found #{length(matches)} exact matches before removing duplicates")

    # Remove duplicates and ensure one-to-one matching
    unique_matches = remove_duplicate_matches(matches)

    Logger.info("After removing duplicates: #{length(unique_matches)} unique exact matches")

    matched_a_ids = unique_matches |> Enum.map(& &1.transaction_a.id) |> MapSet.new()
    matched_b_ids = unique_matches |> Enum.map(& &1.transaction_b.id) |> MapSet.new()

    remaining_a = Enum.reject(file_a_transactions, &MapSet.member?(matched_a_ids, &1.id))
    remaining_b = Enum.reject(file_b_transactions, &MapSet.member?(matched_b_ids, &1.id))

    Logger.info("Remaining unmatched: #{length(remaining_a)} from File A, #{length(remaining_b)} from File B")

    {unique_matches, remaining_a, remaining_b}
  end

  # Check if two transactions match exactly on ALL fields
  defp transactions_match_exactly?(first_transaction, second_transaction, settings) do
    try do
      # Check each field for exact match
      amount_matches = amounts_match_within_tolerance?(first_transaction, second_transaction, settings.amount_tolerance)
      date_matches = dates_match_within_tolerance?(first_transaction, second_transaction, settings.date_tolerance_days)
      reference_matches = fields_match_exactly?(first_transaction.reference, second_transaction.reference)
      type_matches = fields_match_exactly?(first_transaction.transaction_type, second_transaction.transaction_type)
      id_matches = fields_match_exactly?(first_transaction.transaction_id, second_transaction.transaction_id)
      account_matches = fields_match_exactly?(first_transaction.account, second_transaction.account)
      description_matches = fields_match_exactly?(first_transaction.description, second_transaction.description)

      # ALL fields must match for exact match
      if amount_matches and date_matches and reference_matches and type_matches and id_matches and account_matches and description_matches do
        criteria = ["amount", "date", "reference", "transaction_type", "transaction_id", "account", "description"]
        {:match, criteria}
      else
        {:no_match, "Not all fields match exactly"}
      end
    rescue
      error ->
        {:error, "Exception during matching: #{inspect(error)}"}
    end
  end

  # Check if amounts match within the allowed tolerance
  defp amounts_match_within_tolerance?(first_transaction, second_transaction, tolerance) do
    Transaction.amount_match?(first_transaction, second_transaction, tolerance)
  end

  # Check if dates match within the allowed tolerance
  defp dates_match_within_tolerance?(first_transaction, second_transaction, tolerance_days) do
    Transaction.date_match?(first_transaction, second_transaction, tolerance_days)
  end

  # Check if two field values match exactly (both must have values and be equal)
  defp fields_match_exactly?(nil, nil), do: false  # Both nil = no match
  defp fields_match_exactly?(nil, _), do: false    # One nil = no match
  defp fields_match_exactly?(_, nil), do: false    # One nil = no match
  defp fields_match_exactly?(value1, value2) when value1 == value2, do: true
  defp fields_match_exactly?(_, _), do: false

  # Remove duplicate matches (ensure each transaction only appears in one match)
  defp remove_duplicate_matches(matches) do
    # Sort by confidence score (highest first)
    sorted_matches = Enum.sort_by(matches, & Decimal.to_float(&1.confidence_score), :desc)

    {final_matches, _, _} = Enum.reduce(sorted_matches, {[], MapSet.new(), MapSet.new()},
      fn match, {accepted_matches, used_file_a_ids, used_file_b_ids} ->
        file_a_id = match.transaction_a.id
        file_b_id = match.transaction_b.id

        if MapSet.member?(used_file_a_ids, file_a_id) or MapSet.member?(used_file_b_ids, file_b_id) do
          # Skip this match as one of the transactions is already matched
          {accepted_matches, used_file_a_ids, used_file_b_ids}
        else
          # Accept this match
          {[match | accepted_matches], MapSet.put(used_file_a_ids, file_a_id), MapSet.put(used_file_b_ids, file_b_id)}
        end
      end)

    Enum.reverse(final_matches)
  end

  # Save all matches to the database and update transaction statuses
  defp save_matches_to_database(matches, reconciliation_run_id) do
    Repo.transaction(fn ->
      # First, get ALL transactions for this reconciliation run
      all_transactions = Reconciliation.get_transactions(reconciliation_run_id)

      # Initialize ALL transactions as unmatched first
      Logger.info("Initializing match status for #{length(all_transactions)} transactions")
      Enum.each(all_transactions, fn transaction ->
        try do
          Reconciliation.update_transaction_match_status(transaction, %{
            is_matched: false,
            match_confidence: nil
          })
        rescue
          error ->
            Logger.error("Error initializing transaction #{transaction.id} status: #{inspect(error)}")
        end
      end)

      # Track which transactions get matched
      matched_file_a_ids = Enum.map(matches, & &1.transaction_a.id) |> MapSet.new()
      matched_file_b_ids = Enum.map(matches, & &1.transaction_b.id) |> MapSet.new()

      Logger.info("Processing #{length(matches)} matches")

      # Process each match
      Enum.each(matches, fn match ->
        try do
          # Create transaction match record
          match_attributes = %{
            reconciliation_run_id: reconciliation_run_id,
            transaction_a_id: match.transaction_a.id,
            transaction_b_id: match.transaction_b.id,
            match_type: match.match_type,
            confidence_score: match.confidence_score,
            amount_difference: TransactionMatch.calculate_amount_difference(
              match.transaction_a,
              match.transaction_b
            ),
            date_difference_days: TransactionMatch.calculate_date_difference(
              match.transaction_a,
              match.transaction_b
            ),
            matching_criteria: match.matching_criteria
          }

          case Reconciliation.create_transaction_match(match_attributes) do
            {:ok, _transaction_match} ->
              Logger.debug("Successfully created match for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id})")

              # Update both transactions as matched
              try do
                Reconciliation.update_transaction_match_status(match.transaction_a, %{
                  is_matched: true,
                  match_confidence: match.confidence_score
                })

                Reconciliation.update_transaction_match_status(match.transaction_b, %{
                  is_matched: true,
                  match_confidence: match.confidence_score
                })
              rescue
                error ->
                  Logger.error("Error updating matched status for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id}): #{inspect(error)}")
              end

            {:error, changeset} ->
              Logger.error("Failed to create transaction match for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id}): #{inspect(changeset.errors)}")
          end
        rescue
          error ->
            Logger.error("Exception creating match for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id}): #{inspect(error)}")
        end
      end)

      # Log final status
      final_matched_count = MapSet.size(matched_file_a_ids) + MapSet.size(matched_file_b_ids)
      final_unmatched_count = length(all_transactions) - final_matched_count

      Logger.info("Match processing complete: #{final_matched_count} matched, #{final_unmatched_count} unmatched")

      # Double-check: ensure any remaining transactions are marked as unmatched
      # This is a safety net in case some transactions were missed
      try do
        remaining_transactions = Enum.filter(all_transactions, fn transaction ->
          not (MapSet.member?(matched_file_a_ids, transaction.id) or MapSet.member?(matched_file_b_ids, transaction.id))
        end)

        if length(remaining_transactions) > 0 do
          Logger.info("Double-checking #{length(remaining_transactions)} remaining unmatched transactions")
          Enum.each(remaining_transactions, fn transaction ->
            Reconciliation.update_transaction_match_status(transaction, %{
              is_matched: false,
              match_confidence: nil
            })
          end)
        end
      rescue
        error ->
          Logger.error("Error in final unmatched transaction update: #{inspect(error)}")
      end
    end)
  end

  @doc """
  Manually creates a match between two transactions.
  """
  def create_manual_match(transaction_a_id, transaction_b_id, reconciliation_run_id, notes \\ nil) do
    try do
      # Get transactions
      transaction_a = Repo.get!(Transaction, transaction_a_id)
      transaction_b = Repo.get!(Transaction, transaction_b_id)

      # Verify they belong to different files
      if transaction_a.uploaded_file.file_type == transaction_b.uploaded_file.file_type do
        {:error, "Cannot match transactions from the same file"}
      else
        Repo.transaction(fn ->
          # Create manual match
          match_changeset = TransactionMatch.create_manual_match(
            transaction_a,
            transaction_b,
            reconciliation_run_id,
            notes
          )

          case Repo.insert(match_changeset) do
            {:ok, match} ->
              # Update transaction statuses
              Reconciliation.update_transaction_match_status(transaction_a, %{
                is_matched: true,
                match_confidence: Decimal.new("100")
              })

              Reconciliation.update_transaction_match_status(transaction_b, %{
                is_matched: true,
                match_confidence: Decimal.new("100")
              })

              # Recalculate reconciliation stats
              Reconciliation.calculate_reconciliation_stats(reconciliation_run_id)

              {:ok, match}

            {:error, changeset} ->
              {:error, changeset}
          end
        end)
      end
    rescue
      error ->
        Logger.error("Exception in create_manual_match: #{inspect(error)}")
        {:error, "Failed to create manual match: #{inspect(error)}"}
    end
  end

  @doc """
  Removes a match between two transactions.
  """
  def remove_match(match_id) do
    try do
      match = Repo.get!(TransactionMatch, match_id)
      |> Repo.preload([:transaction_a, :transaction_b])

      Repo.transaction(fn ->
        # Update transaction statuses
        Reconciliation.update_transaction_match_status(match.transaction_a, %{
          is_matched: false,
          match_confidence: nil
        })

        Reconciliation.update_transaction_match_status(match.transaction_b, %{
          is_matched: false,
          match_confidence: nil
        })

        # Delete the match
        Repo.delete!(match)

        # Recalculate reconciliation stats
        Reconciliation.calculate_reconciliation_stats(match.reconciliation_run_id)
      end)
    rescue
      error ->
        Logger.error("Exception in remove_match: #{inspect(error)}")
        {:error, "Failed to remove match: #{inspect(error)}"}
    end
  end
end
