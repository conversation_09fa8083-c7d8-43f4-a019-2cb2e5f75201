defmodule Reconciliation.Services.TransactionService do
  @moduledoc """
  Service for transaction-related operations.
  Extracted from LiveView modules to follow proper separation of concerns.
  """

  import Ecto.Query
  alias Reconciliation.{Transaction, ReconciliationRun, UploadedFile, Repo}
  alias Reconciliation.Services.ActivityLogger

  @doc """
  Get all transactions for a user's reconciliation runs
  """
  def get_user_transactions(user_id) do
    user_id
    |> Reconciliation.list_reconciliation_runs()
    |> Enum.flat_map(&Reconciliation.get_transactions(&1.id))
    |> sort_transactions_by_reference_and_file()
  end

  @doc """
  Get unique transaction dates for filtering
  """
  def get_unique_transaction_dates(transactions) do
    transactions
    |> Enum.map(fn t -> 
      case t.transaction_date do
        %Date{} = date -> date
        %DateTime{} = datetime -> DateTime.to_date(datetime)
        %NaiveDateTime{} = naive_datetime -> NaiveDateTime.to_date(naive_datetime)
        _ -> nil
      end
    end)
    |> Enum.filter(&(&1 != nil))
    |> Enum.uniq()
    |> Enum.sort(Date)
  end

  @doc """
  Calculate transaction totals for summary display
  """
  def calculate_transaction_totals(user_id, run_filter \\ nil) do
    # Build query based on filters - include uploaded_file for file source breakdown
    query = from(t in Transaction,
      join: r in ReconciliationRun, on: t.reconciliation_run_id == r.id,
      join: uf in assoc(t, :uploaded_file),
      where: r.user_id == ^user_id,
      preload: [uploaded_file: uf]
    )

    query = if run_filter do
      from(t in query, where: t.reconciliation_run_id == ^run_filter)
    else
      query
    end

    transactions = Repo.all(query)

    # Calculate overall totals
    {total_debits, total_credits} =
      Enum.reduce(transactions, {Decimal.new(0), Decimal.new(0)}, fn transaction, {debits_acc, credits_acc} ->
        cond do
          Decimal.negative?(transaction.amount) ->
            {Decimal.add(debits_acc, Decimal.abs(transaction.amount)), credits_acc}
          Decimal.positive?(transaction.amount) ->
            {debits_acc, Decimal.add(credits_acc, transaction.amount)}
          true ->
            {debits_acc, credits_acc}
        end
      end)

    # Calculate currency-based totals
    currency_totals = calculate_currency_totals(transactions)

    # Calculate file source totals
    file_source_totals = calculate_file_source_totals(transactions)

    # Calculate file source totals with currency breakdown
    file_source_currency_totals = calculate_file_source_currency_totals(transactions)

    # Calculate currency net balances by file for comparison
    currency_net_balances = calculate_currency_net_balances(transactions)

    %{
      total_debits: total_debits,
      total_credits: total_credits,
      currency_totals: currency_totals,
      file_source_totals: file_source_totals,
      file_source_currency_totals: file_source_currency_totals,
      currency_net_balances: currency_net_balances
    }
  end

  @doc """
  Apply filters to transactions
  """
  def apply_filters(transactions, filters) do
    transactions
    |> filter_by_date(filters[:selected_date])
    |> filter_by_status(filters[:selected_status])
    |> filter_by_type(filters[:selected_type])
    |> filter_by_search(filters[:search_query])
  end

  @doc """
  Sort transactions by reference and file type
  """
  def sort_transactions_by_reference_and_file(transactions) do
    transactions
    |> Enum.sort_by(fn t ->
      file_type_order = case t.uploaded_file.file_type do
        "file_a" -> 1
        "file_b" -> 2
        _ -> 3
      end
      {t.reference || "", file_type_order}
    end)
  end

  @doc """
  Run matching engine for a reconciliation run
  """
  def run_matching_engine(run_id, user_id) do
    require Logger
    
    try do
      # Convert string to integer if needed
      run_id_int = if is_binary(run_id), do: String.to_integer(run_id), else: run_id

      run = Reconciliation.get_reconciliation_run!(run_id_int)
      Logger.info("Starting matching for run #{run_id_int}")

      # Get current settings and temporarily enable fuzzy matching for better results
      {:ok, settings} = Reconciliation.get_or_create_settings(run.user_id)
      Logger.info("Current settings: exact=#{settings.auto_match_exact}, fuzzy=#{settings.auto_match_fuzzy}")

      # Temporarily enable fuzzy matching if it's disabled
      if not settings.auto_match_fuzzy do
        Logger.info("Temporarily enabling fuzzy matching for better results")
        {:ok, _updated_settings} = Reconciliation.update_settings(settings, %{auto_match_fuzzy: true})
      end

      # Run the matching engine
      {:ok, matches} = Reconciliation.Services.MatchingEngine.match_transactions(run)
      Logger.info("Matching completed. Found #{length(matches)} matches")

      # Update the reconciliation run status to completed
      {:ok, _updated_run} = Reconciliation.update_reconciliation_run(run, %{status: "completed"})

      # Log the matching activity
      ActivityLogger.log_reconciliation_activity(
        user_id,
        "run_matching",
        resource_type: "reconciliation_run",
        resource_id: run_id_int,
        organization_id: run.user.organization_id,
        metadata: %{
          matches_found: length(matches),
          run_name: run.name
        }
      )

      {:ok, matches}
    rescue
      error ->
        Logger.error("Error running matching: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end

  # Private helper functions

  defp calculate_currency_totals(transactions) do
    transactions
    |> Enum.group_by(fn transaction ->
      transaction.currency || "USD" # Default to USD if no currency
    end)
    |> Enum.map(fn {currency, currency_transactions} ->
      {debit_count, debit_total, credit_count, credit_total} =
        Enum.reduce(currency_transactions, {0, Decimal.new(0), 0, Decimal.new(0)},
          fn transaction, {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc} ->
            cond do
              Decimal.negative?(transaction.amount) ->
                {debit_count_acc + 1,
                 Decimal.add(debit_total_acc, Decimal.abs(transaction.amount)),
                 credit_count_acc,
                 credit_total_acc}
              Decimal.positive?(transaction.amount) ->
                {debit_count_acc,
                 debit_total_acc,
                 credit_count_acc + 1,
                 Decimal.add(credit_total_acc, transaction.amount)}
              true ->
                {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc}
            end
          end)

      %{
        currency: currency,
        debit_count: debit_count,
        debit_total: debit_total,
        credit_count: credit_count,
        credit_total: credit_total,
        net_balance: Decimal.sub(credit_total, debit_total)
      }
    end)
    |> Enum.sort_by(& &1.currency)
  end

  defp calculate_file_source_totals(transactions) do
    transactions
    |> Enum.group_by(fn transaction ->
      case transaction.uploaded_file do
        %{file_type: "file_a"} = uploaded_file ->
          Reconciliation.UploadedFile.display_name(uploaded_file)
        %{file_type: "file_b"} = uploaded_file ->
          Reconciliation.UploadedFile.display_name(uploaded_file)
        _ -> "Unknown"
      end
    end)
    |> Enum.map(fn {file_source, file_transactions} ->
      {debit_count, debit_total, credit_count, credit_total} =
        Enum.reduce(file_transactions, {0, Decimal.new(0), 0, Decimal.new(0)},
          fn transaction, {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc} ->
            cond do
              Decimal.negative?(transaction.amount) ->
                {debit_count_acc + 1,
                 Decimal.add(debit_total_acc, Decimal.abs(transaction.amount)),
                 credit_count_acc,
                 credit_total_acc}
              Decimal.positive?(transaction.amount) ->
                {debit_count_acc,
                 debit_total_acc,
                 credit_count_acc + 1,
                 Decimal.add(credit_total_acc, transaction.amount)}
              true ->
                {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc}
            end
          end)

      %{
        file_source: file_source,
        debit_count: debit_count,
        debit_total: debit_total,
        credit_count: credit_count,
        credit_total: credit_total,
        net_balance: Decimal.sub(credit_total, debit_total)
      }
    end)
  end

  defp calculate_file_source_currency_totals(transactions) do
    # Group by file source and currency
    transactions
    |> Enum.group_by(fn transaction ->
      file_source = case transaction.uploaded_file do
        %{file_type: "file_a"} = uploaded_file ->
          Reconciliation.UploadedFile.display_name(uploaded_file)
        %{file_type: "file_b"} = uploaded_file ->
          Reconciliation.UploadedFile.display_name(uploaded_file)
        _ -> "Unknown"
      end
      currency = transaction.currency || "USD"
      {file_source, currency}
    end)
    |> Enum.map(fn {{file_source, currency}, currency_transactions} ->
      {debit_count, debit_total, credit_count, credit_total} =
        Enum.reduce(currency_transactions, {0, Decimal.new(0), 0, Decimal.new(0)},
          fn transaction, {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc} ->
            cond do
              Decimal.negative?(transaction.amount) ->
                {debit_count_acc + 1,
                 Decimal.add(debit_total_acc, Decimal.abs(transaction.amount)),
                 credit_count_acc,
                 credit_total_acc}
              Decimal.positive?(transaction.amount) ->
                {debit_count_acc,
                 debit_total_acc,
                 credit_count_acc + 1,
                 Decimal.add(credit_total_acc, transaction.amount)}
              true ->
                {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc}
            end
          end)

      %{
        file_source: file_source,
        currency: currency,
        debit_count: debit_count,
        debit_total: debit_total,
        credit_count: credit_count,
        credit_total: credit_total,
        net_balance: Decimal.sub(credit_total, debit_total)
      }
    end)
  end

  defp calculate_currency_net_balances(transactions) do
    # Calculate currency reconciliation analysis with separate credit/debit columns for each file
    # Group transactions by currency first
    transactions
    |> Enum.group_by(fn transaction ->
      transaction.currency || "USD"
    end)
    |> Enum.map(fn {currency, currency_transactions} ->
      # For each currency, separate by file type and then by credit/debit
      file_totals = currency_transactions
      |> Enum.group_by(fn transaction ->
        case transaction.uploaded_file do
          %{file_type: file_type} -> file_type
          _ -> "unknown"
        end
      end)
      |> Enum.reduce(%{}, fn {file_type, file_transactions}, acc ->
        # Separate credits (positive) and debits (negative) for this file
        {credits, debits} = Enum.split_with(file_transactions, fn transaction ->
          Decimal.positive?(transaction.amount)
        end)

        credit_total = Enum.reduce(credits, Decimal.new("0"), fn transaction, sum ->
          Decimal.add(sum, transaction.amount)
        end)

        debit_total = Enum.reduce(debits, Decimal.new("0"), fn transaction, sum ->
          Decimal.add(sum, Decimal.abs(transaction.amount))  # Make debits positive for display
        end)

        Map.put(acc, file_type, %{
          credit_total: credit_total,
          debit_total: debit_total
        })
      end)

      # Get File A and File B data, defaulting to zero if not present
      file_a_data = Map.get(file_totals, "file_a", %{credit_total: Decimal.new("0"), debit_total: Decimal.new("0")})
      file_b_data = Map.get(file_totals, "file_b", %{credit_total: Decimal.new("0"), debit_total: Decimal.new("0")})

      # Calculate net balances, credit difference, and debit difference
      file_a_net = Decimal.sub(file_a_data.credit_total, file_a_data.debit_total)
      file_b_net = Decimal.sub(file_b_data.credit_total, file_b_data.debit_total)
      difference = Decimal.sub(file_a_net, file_b_net)
      credit_diff = Decimal.sub(file_a_data.credit_total, file_b_data.credit_total)
      debit_diff = Decimal.sub(file_a_data.debit_total, file_b_data.debit_total)

      %{
        currency: currency,
        file_a_credit: file_a_data.credit_total,
        file_a_debit: file_a_data.debit_total,
        file_b_credit: file_b_data.credit_total,
        file_b_debit: file_b_data.debit_total,
        credit_diff: credit_diff,
        debit_diff: debit_diff,
        difference: difference
      }
    end)
    |> Enum.sort_by(& &1.currency)
  end

  defp filter_by_date(transactions, nil), do: transactions
  defp filter_by_date(transactions, date) when is_binary(date) do
    case Date.from_iso8601(date) do
      {:ok, filter_date} ->
        Enum.filter(transactions, fn t ->
          transaction_date = case t.transaction_date do
            %Date{} = d -> d
            %DateTime{} = dt -> DateTime.to_date(dt)
            %NaiveDateTime{} = ndt -> NaiveDateTime.to_date(ndt)
            _ -> nil
          end
          transaction_date == filter_date
        end)
      _ -> transactions
    end
  end

  defp filter_by_status(transactions, nil), do: transactions
  defp filter_by_status(transactions, ""), do: transactions
  defp filter_by_status(transactions, status) do
    Enum.filter(transactions, fn t ->
      case status do
        "matched" -> t.is_matched
        "unmatched" -> not t.is_matched
        _ -> true
      end
    end)
  end

  defp filter_by_type(transactions, nil), do: transactions
  defp filter_by_type(transactions, ""), do: transactions
  defp filter_by_type(transactions, type) do
    Enum.filter(transactions, fn t ->
      case type do
        "debit" -> Decimal.negative?(t.amount)
        "credit" -> Decimal.positive?(t.amount)
        _ -> true
      end
    end)
  end

  defp filter_by_search(transactions, nil), do: transactions
  defp filter_by_search(transactions, ""), do: transactions
  defp filter_by_search(transactions, query) do
    query_lower = String.downcase(query)
    Enum.filter(transactions, fn t ->
      String.contains?(String.downcase(t.description || ""), query_lower) or
      String.contains?(String.downcase(t.reference || ""), query_lower)
    end)
  end
end
