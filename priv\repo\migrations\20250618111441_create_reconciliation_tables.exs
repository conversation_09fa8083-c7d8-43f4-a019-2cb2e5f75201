defmodule Reconciliation.Repo.Migrations.CreateReconciliationTables do
  use Ecto.Migration

  def change do
    # Reconciliation Runs - Main container for each reconciliation process
    create table(:reconciliation_runs) do
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :name, :string, null: false
      add :description, :text
      add :status, :string, null: false, default: "pending"
      add :total_transactions_a, :integer, default: 0
      add :total_transactions_b, :integer, default: 0
      add :matched_count, :integer, default: 0
      add :unmatched_a_count, :integer, default: 0
      add :unmatched_b_count, :integer, default: 0
      add :total_amount_a, :decimal, precision: 15, scale: 2, default: 0
      add :total_amount_b, :decimal, precision: 15, scale: 2, default: 0
      add :difference_amount, :decimal, precision: 15, scale: 2, default: 0
      add :match_rate, :decimal, precision: 5, scale: 2, default: 0
      add :processed_at, :utc_datetime
      add :error_message, :text

      timestamps(type: :utc_datetime)
    end

    create index(:reconciliation_runs, [:user_id])
    create index(:reconciliation_runs, [:status])
    create index(:reconciliation_runs, [:processed_at])

    # Uploaded Files - Track each file uploaded for reconciliation
    create table(:uploaded_files) do
      add :reconciliation_run_id, references(:reconciliation_runs, on_delete: :delete_all), null: false
      add :file_type, :string, null: false # "file_a" or "file_b"
      add :filename, :string, null: false
      add :original_filename, :string, null: false
      add :file_size, :bigint
      add :mime_type, :string
      add :file_path, :string
      add :status, :string, null: false, default: "uploaded"
      add :total_rows, :integer, default: 0
      add :processed_rows, :integer, default: 0
      add :error_rows, :integer, default: 0
      add :headers_detected, {:array, :string}
      add :column_mapping, :map # JSON mapping of detected columns
      add :processing_errors, {:array, :string}
      add :processed_at, :utc_datetime

      timestamps(type: :utc_datetime)
    end

    create index(:uploaded_files, [:reconciliation_run_id])
    create index(:uploaded_files, [:file_type])
    create index(:uploaded_files, [:status])

    # Transactions - Individual transaction records from uploaded files
    create table(:transactions) do
      add :uploaded_file_id, references(:uploaded_files, on_delete: :delete_all), null: false
      add :reconciliation_run_id, references(:reconciliation_runs, on_delete: :delete_all), null: false
      add :row_number, :integer, null: false
      add :transaction_date, :date
      add :transaction_id, :string
      add :reference, :string
      add :description, :text
      add :amount, :decimal, precision: 15, scale: 2, null: false
      add :transaction_type, :string # "debit", "credit", etc.
      add :account, :string
      add :category, :string
      add :currency, :string, default: "USD"
      add :raw_data, :map # Store original row data as JSON
      add :is_matched, :boolean, default: false
      add :match_confidence, :decimal, precision: 5, scale: 2
      add :validation_errors, {:array, :string}

      timestamps(type: :utc_datetime)
    end

    create index(:transactions, [:uploaded_file_id])
    create index(:transactions, [:reconciliation_run_id])
    create index(:transactions, [:transaction_date])
    create index(:transactions, [:amount])
    create index(:transactions, [:reference])
    create index(:transactions, [:transaction_id])
    create index(:transactions, [:is_matched])

    # Transaction Matches - Links between matching transactions from file A and B
    create table(:transaction_matches) do
      add :reconciliation_run_id, references(:reconciliation_runs, on_delete: :delete_all), null: false
      add :transaction_a_id, references(:transactions, on_delete: :delete_all), null: false
      add :transaction_b_id, references(:transactions, on_delete: :delete_all), null: false
      add :match_type, :string, null: false # "exact", "fuzzy", "manual", "partial"
      add :confidence_score, :decimal, precision: 5, scale: 2, null: false
      add :amount_difference, :decimal, precision: 15, scale: 2, default: 0
      add :date_difference_days, :integer, default: 0
      add :matching_criteria, {:array, :string} # ["amount", "date", "reference", etc.]
      add :notes, :text
      add :verified_by_user, :boolean, default: false
      add :verified_at, :utc_datetime

      timestamps(type: :utc_datetime)
    end

    create index(:transaction_matches, [:reconciliation_run_id])
    create index(:transaction_matches, [:transaction_a_id])
    create index(:transaction_matches, [:transaction_b_id])
    create index(:transaction_matches, [:match_type])
    create index(:transaction_matches, [:confidence_score])
    create unique_index(:transaction_matches, [:transaction_a_id, :transaction_b_id])

    # Reconciliation Settings - User preferences for matching criteria
    create table(:reconciliation_settings) do
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :amount_tolerance, :decimal, precision: 5, scale: 2, default: 0.01
      add :date_tolerance_days, :integer, default: 3
      add :fuzzy_match_threshold, :decimal, precision: 5, scale: 2, default: 0.8
      add :auto_match_exact, :boolean, default: true
      add :auto_match_fuzzy, :boolean, default: false
      add :default_currency, :string, default: "USD"
      add :preferred_date_format, :string
      add :column_preferences, :map # JSON for preferred column mappings

      timestamps(type: :utc_datetime)
    end

    create unique_index(:reconciliation_settings, [:user_id])
  end
end
