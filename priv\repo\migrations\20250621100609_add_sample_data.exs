defmodule Reconciliation.Repo.Migrations.AddSampleData do
  use Ecto.Migration
  import Ecto.Query

  def up do
    # Update existing reconciliation runs with realistic sample data
    execute """
    UPDATE reconciliation_runs
    SET
      status = 'completed',
      total_transactions_a = 150,
      total_transactions_b = 148,
      matched_count = 142,
      unmatched_a_count = 8,
      unmatched_b_count = 6,
      total_amount_a = 45678.90,
      total_amount_b = 45234.50,
      difference_amount = 444.40,
      match_rate = 95.30,
      processed_at = NOW()
    WHERE status = 'pending' AND name LIKE 'Reconciliation%'
    """
  end

  def down do
    # Reset the data back to defaults
    execute """
    UPDATE reconciliation_runs
    SET
      status = 'pending',
      total_transactions_a = 0,
      total_transactions_b = 0,
      matched_count = 0,
      unmatched_a_count = 0,
      unmatched_b_count = 0,
      total_amount_a = 0,
      total_amount_b = 0,
      difference_amount = 0,
      match_rate = 0,
      processed_at = NULL
    WHERE name LIKE 'Reconciliation%'
    """
  end
end
