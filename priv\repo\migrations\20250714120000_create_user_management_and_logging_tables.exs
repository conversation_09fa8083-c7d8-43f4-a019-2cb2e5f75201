defmodule Reconciliation.Repo.Migrations.CreateUserManagementAndLoggingTables do
  use Ecto.Migration

  def change do
    # Organizations table for multi-tenant support
    create table(:organizations) do
      add :name, :string, null: false
      add :slug, :string, null: false
      add :description, :text
      add :settings, :map, default: %{}
      add :status, :string, default: "active"

      timestamps(type: :utc_datetime)
    end

    create unique_index(:organizations, [:slug])
    create index(:organizations, [:status])

    # Teams table for organization structure
    create table(:teams) do
      add :organization_id, references(:organizations, on_delete: :delete_all), null: false
      add :name, :string, null: false
      add :description, :text
      add :settings, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    create index(:teams, [:organization_id])
    create unique_index(:teams, [:organization_id, :name])

    # Roles table for role-based access control
    create table(:roles) do
      add :name, :string, null: false
      add :description, :text
      add :permissions, :map, default: %{}
      add :is_system_role, :boolean, default: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:roles, [:name])

    # Permissions table for granular access control
    create table(:permissions) do
      add :name, :string, null: false
      add :resource, :string, null: false
      add :action, :string, null: false
      add :description, :text

      timestamps(type: :utc_datetime)
    end

    create unique_index(:permissions, [:name])
    create unique_index(:permissions, [:resource, :action])

    # Role permissions junction table
    create table(:role_permissions) do
      add :role_id, references(:roles, on_delete: :delete_all), null: false
      add :permission_id, references(:permissions, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create unique_index(:role_permissions, [:role_id, :permission_id])

    # Enhance users table with additional fields
    alter table(:users) do
      add :status, :string, default: "active"
      add :organization_id, references(:organizations, on_delete: :nilify_all)
      add :last_login_at, :utc_datetime
      add :failed_login_attempts, :integer, default: 0
      add :locked_until, :utc_datetime
      add :password_changed_at, :utc_datetime
      add :must_change_password, :boolean, default: false
    end

    create index(:users, [:organization_id])
    create index(:users, [:status])
    create index(:users, [:last_login_at])

    # User profiles table for extended user information
    create table(:user_profiles) do
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :first_name, :string
      add :last_name, :string
      add :phone_number, :string
      add :job_title, :string
      add :department, :string
      add :profile_picture_url, :string
      add :timezone, :string, default: "UTC"
      add :language, :string, default: "en"
      add :notification_preferences, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    create unique_index(:user_profiles, [:user_id])

    # User role assignments table
    create table(:user_role_assignments) do
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :role_id, references(:roles, on_delete: :delete_all), null: false
      add :organization_id, references(:organizations, on_delete: :delete_all)
      add :assigned_by, references(:users, on_delete: :nilify_all)
      add :assigned_at, :utc_datetime, null: false
      add :expires_at, :utc_datetime

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create unique_index(:user_role_assignments, [:user_id, :role_id, :organization_id])
    create index(:user_role_assignments, [:user_id])
    create index(:user_role_assignments, [:role_id])
    create index(:user_role_assignments, [:organization_id])
    create index(:user_role_assignments, [:assigned_by])

    # User team memberships table
    create table(:user_team_memberships) do
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :team_id, references(:teams, on_delete: :delete_all), null: false
      add :role, :string, default: "member"

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create unique_index(:user_team_memberships, [:user_id, :team_id])
    create index(:user_team_memberships, [:team_id])

    # User activity logs table for comprehensive activity tracking
    create table(:user_activity_logs) do
      add :user_id, references(:users, on_delete: :delete_all)
      add :session_id, :string
      add :activity_type, :string, null: false
      add :action, :string, null: false
      add :resource_type, :string
      add :resource_id, :bigint
      add :ip_address, :string
      add :user_agent, :text
      add :request_path, :string
      add :request_method, :string
      add :response_status, :integer
      add :duration_ms, :integer
      add :metadata, :map, default: %{}
      add :organization_id, references(:organizations, on_delete: :delete_all)

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create index(:user_activity_logs, [:user_id])
    create index(:user_activity_logs, [:activity_type])
    create index(:user_activity_logs, [:action])
    create index(:user_activity_logs, [:resource_type, :resource_id])
    create index(:user_activity_logs, [:organization_id])
    create index(:user_activity_logs, [:inserted_at])
    create index(:user_activity_logs, [:session_id])

    # Login attempts table for security monitoring
    create table(:login_attempts) do
      add :email, :string, null: false
      add :ip_address, :string, null: false
      add :user_agent, :text
      add :success, :boolean, null: false
      add :failure_reason, :string
      add :user_id, references(:users, on_delete: :delete_all)

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create index(:login_attempts, [:email])
    create index(:login_attempts, [:ip_address])
    create index(:login_attempts, [:success])
    create index(:login_attempts, [:inserted_at])
    create index(:login_attempts, [:user_id])

    # Enhanced user sessions table
    create table(:user_sessions) do
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :session_token, :string, null: false
      add :ip_address, :string
      add :user_agent, :text
      add :started_at, :utc_datetime, null: false
      add :last_activity_at, :utc_datetime, null: false
      add :ended_at, :utc_datetime
      add :status, :string, default: "active"

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create unique_index(:user_sessions, [:session_token])
    create index(:user_sessions, [:user_id])
    create index(:user_sessions, [:status])
    create index(:user_sessions, [:last_activity_at])

    # Add organization_id to reconciliation_runs for multi-tenant support
    alter table(:reconciliation_runs) do
      add :organization_id, references(:organizations, on_delete: :delete_all)
    end

    create index(:reconciliation_runs, [:organization_id])
  end
end
