defmodule Reconciliation.Repo.Migrations.SeedRolesAndPermissions do
  use Ecto.Migration

  def up do
    # Insert default permissions
    permissions = [
      # User permissions
      %{name: "users:create", resource: "users", action: "create", description: "Create new users"},
      %{name: "users:read", resource: "users", action: "read", description: "View user information"},
      %{name: "users:update", resource: "users", action: "update", description: "Update user information"},
      %{name: "users:delete", resource: "users", action: "delete", description: "Delete users"},
      
      # Organization permissions
      %{name: "organizations:create", resource: "organizations", action: "create", description: "Create new organizations"},
      %{name: "organizations:read", resource: "organizations", action: "read", description: "View organization information"},
      %{name: "organizations:update", resource: "organizations", action: "update", description: "Update organization settings"},
      %{name: "organizations:delete", resource: "organizations", action: "delete", description: "Delete organizations"},
      
      # Team permissions
      %{name: "teams:create", resource: "teams", action: "create", description: "Create new teams"},
      %{name: "teams:read", resource: "teams", action: "read", description: "View team information"},
      %{name: "teams:update", resource: "teams", action: "update", description: "Update team settings"},
      %{name: "teams:delete", resource: "teams", action: "delete", description: "Delete teams"},
      
      # Role permissions
      %{name: "roles:create", resource: "roles", action: "create", description: "Create custom roles"},
      %{name: "roles:read", resource: "roles", action: "read", description: "View role information"},
      %{name: "roles:update", resource: "roles", action: "update", description: "Update role permissions"},
      %{name: "roles:delete", resource: "roles", action: "delete", description: "Delete custom roles"},
      
      # Reconciliation permissions
      %{name: "reconciliations:create", resource: "reconciliations", action: "create", description: "Create new reconciliations"},
      %{name: "reconciliations:read", resource: "reconciliations", action: "read", description: "View reconciliation data"},
      %{name: "reconciliations:update", resource: "reconciliations", action: "update", description: "Update reconciliation settings"},
      %{name: "reconciliations:delete", resource: "reconciliations", action: "delete", description: "Delete reconciliations"},
      
      # Report permissions
      %{name: "reports:create", resource: "reports", action: "create", description: "Generate new reports"},
      %{name: "reports:read", resource: "reports", action: "read", description: "View existing reports"},
      %{name: "reports:update", resource: "reports", action: "update", description: "Modify report settings"},
      %{name: "reports:delete", resource: "reports", action: "delete", description: "Delete reports"},
      
      # Settings permissions
      %{name: "settings:create", resource: "settings", action: "create", description: "Create new settings"},
      %{name: "settings:read", resource: "settings", action: "read", description: "View system settings"},
      %{name: "settings:update", resource: "settings", action: "update", description: "Modify system settings"},
      %{name: "settings:delete", resource: "settings", action: "delete", description: "Delete settings"},
      
      # Activity log permissions
      %{name: "activity_logs:read", resource: "activity_logs", action: "read", description: "View activity logs and audit trails"},
      
      # System permissions
      %{name: "system:manage", resource: "system", action: "manage", description: "Full system administration access"}
    ]

    now = DateTime.utc_now() |> DateTime.truncate(:second)
    permissions_with_timestamps = Enum.map(permissions, fn perm ->
      Map.merge(perm, %{inserted_at: now, updated_at: now})
    end)

    Ecto.Adapters.SQL.query!(
      Reconciliation.Repo,
      "INSERT INTO permissions (name, resource, action, description, inserted_at, updated_at) VALUES " <>
      Enum.map_join(permissions_with_timestamps, ", ", fn perm ->
        "(#{quote_value(perm.name)}, #{quote_value(perm.resource)}, #{quote_value(perm.action)}, #{quote_value(perm.description)}, #{quote_value(perm.inserted_at)}, #{quote_value(perm.updated_at)})"
      end)
    )

    # Insert default roles
    roles = [
      %{
        name: "admin",
        description: "Full system administrator with all permissions",
        permissions: %{
          "users" => ["create", "read", "update", "delete"],
          "organizations" => ["create", "read", "update", "delete"],
          "teams" => ["create", "read", "update", "delete"],
          "roles" => ["create", "read", "update", "delete"],
          "reconciliations" => ["create", "read", "update", "delete"],
          "reports" => ["create", "read", "update", "delete"],
          "settings" => ["create", "read", "update", "delete"],
          "activity_logs" => ["read"],
          "system" => ["manage"]
        },
        is_system_role: true
      },
      %{
        name: "manager",
        description: "Team manager with user and reconciliation management permissions",
        permissions: %{
          "users" => ["read", "update"],
          "teams" => ["create", "read", "update"],
          "reconciliations" => ["create", "read", "update", "delete"],
          "reports" => ["create", "read", "update"],
          "settings" => ["read", "update"],
          "activity_logs" => ["read"]
        },
        is_system_role: true
      },
      %{
        name: "analyst",
        description: "Reconciliation analyst with create and update permissions",
        permissions: %{
          "reconciliations" => ["create", "read", "update"],
          "reports" => ["create", "read"],
          "settings" => ["read"],
          "activity_logs" => ["read"]
        },
        is_system_role: true
      },
      %{
        name: "viewer",
        description: "Read-only access to assigned reconciliations and reports",
        permissions: %{
          "reconciliations" => ["read"],
          "reports" => ["read"],
          "activity_logs" => ["read"]
        },
        is_system_role: true
      }
    ]

    roles_with_timestamps = Enum.map(roles, fn role ->
      Map.merge(role, %{
        permissions: Jason.encode!(role.permissions),
        inserted_at: now,
        updated_at: now
      })
    end)

    Ecto.Adapters.SQL.query!(
      Reconciliation.Repo,
      "INSERT INTO roles (name, description, permissions, is_system_role, inserted_at, updated_at) VALUES " <>
      Enum.map_join(roles_with_timestamps, ", ", fn role ->
        "(#{quote_value(role.name)}, #{quote_value(role.description)}, #{quote_value(role.permissions)}, #{role.is_system_role}, #{quote_value(role.inserted_at)}, #{quote_value(role.updated_at)})"
      end)
    )
  end

  def down do
    execute "DELETE FROM role_permissions"
    execute "DELETE FROM user_role_assignments"
    execute "DELETE FROM roles WHERE is_system_role = true"
    execute "DELETE FROM permissions"
  end

  defp quote_value(value) when is_binary(value), do: "'#{String.replace(value, "'", "''")}'"
  defp quote_value(value) when is_boolean(value), do: to_string(value)
  defp quote_value(%DateTime{} = value), do: "'#{DateTime.to_iso8601(value)}'"
  defp quote_value(value), do: "'#{value}'"
end
