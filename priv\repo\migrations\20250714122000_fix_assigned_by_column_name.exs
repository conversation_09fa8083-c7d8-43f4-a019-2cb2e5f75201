defmodule Reconciliation.Repo.Migrations.FixAssignedByColumnName do
  use Ecto.Migration

  def up do
    # Rename the assigned_by column to assigned_by_id for consistency
    rename table(:user_role_assignments), :assigned_by, to: :assigned_by_id
    
    # Update the index as well
    drop index(:user_role_assignments, [:assigned_by])
    create index(:user_role_assignments, [:assigned_by_id])
  end

  def down do
    # Reverse the changes
    drop index(:user_role_assignments, [:assigned_by_id])
    rename table(:user_role_assignments), :assigned_by_id, to: :assigned_by
    create index(:user_role_assignments, [:assigned_by])
  end
end
