# Transaction Reconciliation Test Suite

## Overview

This comprehensive test suite is designed to validate the enhanced transaction matching system that includes 6 matching criteria: **amount**, **date**, **reference**, **transaction_type**, **transaction_id**, and **account**. The test suite includes various scenarios to ensure the system handles different data conditions, edge cases, and performance requirements.

## Test File Structure

```
test/fixtures/reconciliation_test_data/
├── csv/                    # CSV test files
├── excel/                  # Excel (.xlsx) test files  
├── docs/                   # Documentation
└── convert_csv_to_excel.ps1 # Conversion script
```

## Test Scenarios

### 1. Perfect Match Test Files
**Files:** `perfect_match_file_a.csv`, `perfect_match_file_b.csv`

**Purpose:** Test 100% exact matching capability

**Expected Results:**
- **Total Transactions:** 20 per file
- **Expected Matches:** 20/20 (100% match rate)
- **Matching Criteria:** All 6 criteria should match for each transaction
- **Total Amount:** $39,730.80 per file

**Test Details:**
- Identical transactions in both files
- All fields match exactly: amount, date, reference, transaction_type, transaction_id, account
- Tests the system's ability to achieve perfect reconciliation

### 2. Partial Match Test Files  
**Files:** `partial_match_file_a.csv`, `partial_match_file_b.csv`

**Purpose:** Test fuzzy matching and partial reconciliation

**Expected Results:**
- **File A Transactions:** 22 total
- **File B Transactions:** 22 total  
- **Expected Matches:** 18/22 (≈82% match rate)
- **Unmatched in A:** 2 transactions (TXN021, TXN022)
- **Unmatched in B:** 2 transactions (TXN023, TXN024)
- **Near-matches:** 1 transaction with slight amount difference (TXN004: $25.00 vs $25.01)

**Test Details:**
- Tests tolerance-based matching
- Includes transactions with minor differences within tolerance limits
- Validates fuzzy matching algorithms

### 3. Edge Case Test Files
**Files:** `edge_case_file_a.csv`, `edge_case_file_b.csv`

**Purpose:** Test system handling of boundary conditions and special data

**Expected Results:**
- **Total Transactions:** 20 per file
- **Expected Matches:** 10-15/20 (50-75% match rate)
- **Special Cases Tested:**
  - Missing/null values
  - Unicode characters
  - Special characters and symbols
  - Very small amounts (0.01)
  - Very large amounts (999,999.99)
  - Zero amounts
  - Date edge cases (leap year, month/year boundaries)

**Test Details:**
- Validates data validation and error handling
- Tests nil value processing
- Ensures system stability with unusual data

### 4. Performance Test Files
**Files:** `performance_test_file_a.csv`, `performance_test_file_b.csv`

**Purpose:** Test system performance with larger datasets

**Expected Results:**
- **Total Transactions:** 50 per file
- **Expected Matches:** 50/50 (100% match rate)
- **Processing Time:** Should complete within 30 seconds
- **Memory Usage:** Monitor for memory leaks or excessive usage
- **Total Amount:** $102,503.00 per file

**Test Details:**
- Tests system scalability
- Validates performance with volume data
- Ensures efficient processing algorithms

### 5. Error Case Test Files
**Files:** `error_case_file_a.csv`, `error_case_file_b.csv`

**Purpose:** Test error handling and data validation

**Expected Results:**
- **File A:** Contains invalid data that should trigger validation errors
- **File B:** Contains valid data for comparison
- **Expected Behavior:** System should handle errors gracefully
- **Error Types:** Invalid dates, amounts, transaction types, currencies

**Test Details:**
- Tests system robustness
- Validates error reporting
- Ensures graceful failure handling

### 6. Multi-Currency Test Files
**Files:** `multi_currency_file_a.csv`, `multi_currency_file_b.csv`

**Purpose:** Test multi-currency transaction handling

**Expected Results:**
- **Total Transactions:** 20 per file
- **Currencies Tested:** USD, EUR, GBP, MWK
- **Expected Matches:** 20/20 (100% match rate)
- **Currency Validation:** System should handle different currency codes

**Test Details:**
- Tests currency field processing
- Validates multi-currency reconciliation
- Ensures proper currency handling

## Summary Data Format

Each test file includes summary rows at the bottom:

```csv
Total_Transactions,{count},,,,,,,
Total_Credits,{count},{amount},,,,,,
Total_Debits,{count},{amount},,,,,,
Net_Balance,,,{credits - debits},,,,
Total_Amount,,,{total_amount},,,,
```

## Testing Instructions

### Step 1: Upload Test Files
1. Navigate to the reconciliation upload page
2. Select File A and File B from the same test scenario
3. Click "Upload Files" and wait for processing completion

### Step 2: Review Processing Results
- Check that files are processed without errors
- Verify transaction counts match expected values
- Review any validation errors or warnings

### Step 3: Run Reconciliation
1. Click "Start Reconciliation"
2. Monitor progress indicators
3. Wait for completion

### Step 4: Analyze Results
- Review match statistics
- Check confidence scores
- Verify matching criteria used
- Compare actual vs expected match rates

### Step 5: Validate Summary Data
- Verify total amounts match file summaries
- Check credit/debit breakdowns
- Confirm net balance calculations

## Expected Performance Benchmarks

| Test Scenario | File Size | Expected Processing Time | Expected Match Rate |
|---------------|-----------|-------------------------|-------------------|
| Perfect Match | 20 transactions | < 5 seconds | 100% |
| Partial Match | 22 transactions | < 5 seconds | ~82% |
| Edge Cases | 20 transactions | < 10 seconds | 50-75% |
| Performance | 50 transactions | < 30 seconds | 100% |
| Error Cases | 20 transactions | < 10 seconds | Variable |
| Multi-Currency | 20 transactions | < 5 seconds | 100% |

## Troubleshooting Guide

### Common Issues

**1. File Upload Failures**
- Check file format (CSV/Excel)
- Verify file size limits
- Ensure proper column headers

**2. Processing Errors**
- Review validation error messages
- Check for invalid data formats
- Verify required fields are present

**3. Low Match Rates**
- Adjust tolerance settings
- Review matching criteria configuration
- Check for data quality issues

**4. Performance Issues**
- Monitor system resources
- Check database performance
- Review file size and complexity

### Debug Output Interpretation

The system provides detailed debug information:

```
Matching Criteria Results:
- Amount Match: ✓/✗
- Date Match: ✓/✗
- Reference Match: ✓/✗
- Transaction Type Match: ✓/✗
- Transaction ID Match: ✓/✗
- Account Match: ✓/✗
```

### Configuration Recommendations

**Tolerance Settings:**
- Amount Tolerance: $0.01 (default)
- Date Tolerance: 3 days (default)
- Fuzzy Match Threshold: 0.8 (80%)

**Auto-Match Settings:**
- Enable Exact Matching: Yes
- Enable Fuzzy Matching: No (for testing)

## File Conversion

### CSV to Excel Conversion

Use the provided PowerShell script:

```powershell
.\convert_csv_to_excel.ps1 -CsvDirectory ".\csv" -ExcelDirectory ".\excel"
```

**Requirements:**
- Microsoft Excel installed
- PowerShell execution policy allows scripts

**Alternative:** Manual conversion using Excel's "Open" and "Save As" functions

## Test Data Validation

Before running tests, verify:

1. **File Integrity:** All CSV files open correctly
2. **Data Consistency:** Summary totals match transaction data
3. **Format Compliance:** Headers match system expectations
4. **Character Encoding:** Files use UTF-8 encoding

## Reporting Issues

When reporting test failures, include:

1. Test scenario name
2. Expected vs actual results
3. Error messages or logs
4. System configuration details
5. File processing statistics

## Maintenance

**Regular Updates:**
- Review test data quarterly
- Update expected results as system evolves
- Add new edge cases as discovered
- Maintain documentation accuracy

**Version Control:**
- Track changes to test files
- Document modifications
- Maintain backward compatibility
