defmodule Reconciliation.Services.ExcelParserSummaryTest do
  use Reconciliation.DataCase

  alias Reconciliation.Services.ExcelParser
  alias Reconciliation.{UploadedFile, ReconciliationRun}

  describe "summary row detection and parsing" do
    setup do
      user = Reconciliation.AccountsFixtures.user_fixture()
      
      {:ok, run} = Reconciliation.create_reconciliation_run(%{
        name: "Test Run",
        user_id: user.id,
        status: "pending"
      })

      # Create a test CSV file with summary data
      test_csv_content = """
      Date,Transaction_ID,Reference,Description,Amount,Transaction_Type,Account,Category,Currency
      2024-01-15,TXN001,REF001,Test Transaction 1,100.00,credit,ACC001,Test,USD
      2024-01-16,TXN002,REF002,Test Transaction 2,200.00,debit,ACC001,Test,USD
      2024-01-17,TXN003,REF003,Test Transaction 3,150.00,credit,ACC001,Test,USD
      Total_Transactions,3,,,,,,,
      Total_Credits,2,,,250.00,,,,
      Total_Debits,1,,,200.00,,,,
      Net_Balance,,,50.00,,,,
      Total_Amount,,,450.00,,,,
      """

      # Write test file
      test_file_path = Path.join(System.tmp_dir(), "test_summary_#{:rand.uniform(10000)}.csv")
      File.write!(test_file_path, test_csv_content)

      {:ok, uploaded_file} = Reconciliation.create_uploaded_file(%{
        reconciliation_run_id: run.id,
        file_type: "file_a",
        filename: "test_summary.csv",
        original_filename: "test_summary.csv",
        file_path: test_file_path,
        file_size: byte_size(test_csv_content),
        status: "uploaded"
      })

      on_exit(fn -> File.rm(test_file_path) end)

      %{uploaded_file: uploaded_file, test_file_path: test_file_path}
    end

    test "separates transaction rows from summary rows", %{uploaded_file: uploaded_file} do
      # Test the private function through the public interface
      result = ExcelParser.parse_file(uploaded_file)
      
      assert {:ok, transactions} = result
      assert length(transactions) == 3
      
      # Reload the uploaded file to check summary data
      updated_file = Reconciliation.get_uploaded_file!(uploaded_file.id)
      
      # Check file-provided summary data
      assert updated_file.file_total_transactions == 3
      assert updated_file.file_total_credits == 2
      assert updated_file.file_total_debits == 1
      assert Decimal.equal?(updated_file.file_total_credit_amount, Decimal.new("250.00"))
      assert Decimal.equal?(updated_file.file_total_debit_amount, Decimal.new("200.00"))
      assert Decimal.equal?(updated_file.file_net_balance, Decimal.new("50.00"))
      assert Decimal.equal?(updated_file.file_total_amount, Decimal.new("450.00"))
      
      # Check calculated summary data
      assert updated_file.calculated_total_transactions == 3
      assert updated_file.calculated_total_credits == 2
      assert updated_file.calculated_total_debits == 1
      
      # Check validation
      assert updated_file.summary_validation_passed == true
      assert updated_file.summary_validation_errors == []
    end

    test "detects summary validation errors when totals don't match", %{uploaded_file: uploaded_file} do
      # Create a file with incorrect summary data
      incorrect_csv_content = """
      Date,Transaction_ID,Reference,Description,Amount,Transaction_Type,Account,Category,Currency
      2024-01-15,TXN001,REF001,Test Transaction 1,100.00,credit,ACC001,Test,USD
      2024-01-16,TXN002,REF002,Test Transaction 2,200.00,debit,ACC001,Test,USD
      Total_Transactions,5,,,,,,,
      Total_Amount,,,999.99,,,,
      """

      # Update the test file
      File.write!(uploaded_file.file_path, incorrect_csv_content)

      result = ExcelParser.parse_file(uploaded_file)
      
      assert {:ok, transactions} = result
      assert length(transactions) == 2
      
      # Reload the uploaded file to check validation
      updated_file = Reconciliation.get_uploaded_file!(uploaded_file.id)
      
      # Check validation failed
      assert updated_file.summary_validation_passed == false
      assert length(updated_file.summary_validation_errors) > 0
    end
  end
end
