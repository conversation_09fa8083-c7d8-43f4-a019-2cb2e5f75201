defmodule Reconciliation.TransactionMatchingFixTest do
  use Reconciliation.DataCase

  alias Reconciliation.{Transaction, TransactionMatch}

  describe "amount matching with mixed positive/negative amounts" do
    test "amount_match?/3 handles mixed positive/negative amounts correctly" do
      # This test ensures that debit transactions match regardless of whether
      # they are stored as positive or negative amounts
      
      # Same debit transaction: one stored as positive, one as negative
      txn_positive = %Transaction{amount: Decimal.new("1500.00")}
      txn_negative = %Transaction{amount: Decimal.new("-1500.00")}
      
      # Should match because they represent the same economic transaction
      assert Transaction.amount_match?(txn_positive, txn_negative)
      assert Transaction.amount_match?(txn_negative, txn_positive)
    end

    test "calculate_amount_difference/2 returns zero for same absolute amounts" do
      # This test ensures that matched transactions show zero difference
      # even when stored with different signs
      
      txn_positive = %Transaction{amount: Decimal.new("1500.00")}
      txn_negative = %Transaction{amount: Decimal.new("-1500.00")}
      
      diff = TransactionMatch.calculate_amount_difference(txn_positive, txn_negative)
      assert Decimal.equal?(diff, Decimal.new("0.00"))
    end

    test "amount matching still rejects different absolute amounts" do
      # Ensure we don't break normal functionality
      
      txn_1500 = %Transaction{amount: Decimal.new("1500.00")}
      txn_1600 = %Transaction{amount: Decimal.new("-1600.00")}
      
      refute Transaction.amount_match?(txn_1500, txn_1600)
      
      diff = TransactionMatch.calculate_amount_difference(txn_1500, txn_1600)
      assert Decimal.equal?(diff, Decimal.new("100.00"))
    end

    test "amount matching works with tolerance for mixed signs" do
      # Test tolerance with mixed positive/negative amounts
      
      txn_a = %Transaction{amount: Decimal.new("1500.00")}
      txn_b = %Transaction{amount: Decimal.new("-1500.01")}
      
      # Should match within default tolerance (0.01)
      assert Transaction.amount_match?(txn_a, txn_b)
      
      # Should not match outside tolerance
      txn_c = %Transaction{amount: Decimal.new("-1500.10")}
      refute Transaction.amount_match?(txn_a, txn_c)
    end
  end

  describe "edge cases" do
    test "handles zero amounts correctly" do
      txn_zero_pos = %Transaction{amount: Decimal.new("0.00")}
      txn_zero_neg = %Transaction{amount: Decimal.new("-0.00")}
      
      assert Transaction.amount_match?(txn_zero_pos, txn_zero_neg)
      
      diff = TransactionMatch.calculate_amount_difference(txn_zero_pos, txn_zero_neg)
      assert Decimal.equal?(diff, Decimal.new("0.00"))
    end

    test "preserves existing functionality for same-sign amounts" do
      # Ensure we don't break existing matching for same-sign amounts
      
      # Both positive
      txn_pos_1 = %Transaction{amount: Decimal.new("1500.00")}
      txn_pos_2 = %Transaction{amount: Decimal.new("1500.00")}
      assert Transaction.amount_match?(txn_pos_1, txn_pos_2)
      
      # Both negative
      txn_neg_1 = %Transaction{amount: Decimal.new("-1500.00")}
      txn_neg_2 = %Transaction{amount: Decimal.new("-1500.00")}
      assert Transaction.amount_match?(txn_neg_1, txn_neg_2)
    end
  end
end
