defmodule ReconciliationWeb.ReconciliationUploadLiveTest do
  use ReconciliationWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Reconciliation.AccountsFixtures

  alias Reconciliation.Reconciliation

  setup %{conn: conn} do
    user = user_fixture()
    conn = log_in_user(conn, user)
    
    # Create a reconciliation run for testing
    {:ok, run} = Reconciliation.create_reconciliation_run(%{
      name: "Test Upload Run",
      user_id: user.id,
      status: "pending"
    })
    
    %{conn: conn, user: user, reconciliation_run: run}
  end

  describe "Upload page" do
    test "renders upload page with correct elements", %{conn: conn, reconciliation_run: run} do
      {:ok, _lv, html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # Check page title and description
      assert html =~ "Financial Reconciliation"
      assert html =~ "Upload two files to compare and reconcile transactions"
      
      # Check reconciliation name form
      assert html =~ "Reconciliation Details"
      assert html =~ "Reconciliation Name"
      
      # Check file upload sections
      assert html =~ "Source File A"
      assert html =~ "Source File B"
      assert html =~ "Click to upload File A"
      assert html =~ "Click to upload File B"
      assert html =~ "Or drag and drop files here"
      
      # Check file type restrictions
      assert html =~ "Excel (.xlsx, .xls) or CSV files up to 50MB"
      
      # Check submit button state
      assert html =~ "Please upload both files to continue"
    end

    test "upload areas have correct CSS classes for clickability", %{conn: conn, reconciliation_run: run} do
      {:ok, _lv, html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # Check that upload areas have cursor-pointer class
      assert html =~ "cursor-pointer"
      
      # Check hover effects
      assert html =~ "hover:border-blue-400"
      assert html =~ "hover:border-green-400"
      assert html =~ "hover:bg-blue-50"
      assert html =~ "hover:bg-green-50"
      
      # Check transition classes
      assert html =~ "transition-all duration-200"
      assert html =~ "transition-colors"
    end

    test "file inputs are properly hidden", %{conn: conn, reconciliation_run: run} do
      {:ok, _lv, html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # Check that file inputs have hidden class
      assert html =~ ~r/input[^>]*type="file"[^>]*class="[^"]*hidden[^"]*"/
    end

    test "updates reconciliation name", %{conn: conn, reconciliation_run: run} do
      {:ok, lv, _html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # Update the name
      new_name = "Updated Test Name"
      
      lv
      |> form("#reconciliation-name-form", name: new_name)
      |> render_change()

      # Verify the name was updated in the database
      updated_run = Reconciliation.get_reconciliation_run!(run.id)
      assert updated_run.name == new_name
    end

    test "shows upload progress for files", %{conn: conn, reconciliation_run: run} do
      {:ok, lv, _html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # Create a test CSV file
      csv_content = """
      Date,Description,Amount,Reference
      2024-01-01,Test Transaction,100.00,REF001
      """
      
      file_path = Path.join(System.tmp_dir(), "test_upload.csv")
      File.write!(file_path, csv_content)

      # Simulate file upload (this is a simplified test)
      # In a real test, you'd use the LiveView file upload testing utilities
      
      # Clean up
      File.rm(file_path)
    end

    test "shows error messages for invalid files", %{conn: conn, reconciliation_run: run} do
      {:ok, lv, _html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # The upload validation is handled by Phoenix LiveView's upload system
      # We can test that error display elements are present
      html = render(lv)
      
      # Check that error display areas exist (they'll be populated when errors occur)
      assert html =~ ~r/\:for=\{\{_ref, err\} <- @uploads\.file_a\.errors\}/
      assert html =~ ~r/\:for=\{\{_ref, err\} <- @uploads\.file_b\.errors\}/
    end

    test "cancel upload functionality", %{conn: conn, reconciliation_run: run} do
      {:ok, lv, _html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # Test that cancel upload event handlers are present
      html = render(lv)
      assert html =~ ~r/phx-click="cancel_upload"/
      assert html =~ ~r/phx-value-type="file_a"/
      assert html =~ ~r/phx-value-type="file_b"/
    end

    test "submit button appears when both files are uploaded", %{conn: conn, reconciliation_run: run} do
      {:ok, lv, _html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # Initially, submit button should not be visible
      html = render(lv)
      assert html =~ "Please upload both files to continue"
      refute html =~ "Start Reconciliation"
    end
  end

  describe "Upload functionality" do
    test "handles file upload events", %{conn: conn, reconciliation_run: run} do
      {:ok, lv, _html} = live(conn, ~p"/reconciliation/#{run.id}/upload")

      # Test that the LiveView can handle upload events
      # This tests the event handler structure
      assert has_element?(lv, "[phx-click='cancel_upload']")
    end

    test "processes reconciliation after both files uploaded", %{conn: conn, reconciliation_run: run} do
      # This would be a more complex integration test
      # For now, we'll just verify the event handler exists
      {:ok, lv, _html} = live(conn, ~p"/reconciliation/#{run.id}/upload")
      
      # Check that save event handler exists
      html = render(lv)
      assert html =~ ~r/phx-submit="save"/
    end
  end
end
