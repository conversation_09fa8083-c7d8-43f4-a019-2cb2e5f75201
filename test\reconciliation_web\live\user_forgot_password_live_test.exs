defmodule ReconciliationWeb.UserForgotPasswordLiveTest do
  use ReconciliationWeb.ConnCase
  import Phoenix.LiveViewTest
  import Swoosh.TestAssertions

  alias Reconciliation.Accounts

  describe "Forgot password page" do
    test "renders forgot password form", %{conn: conn} do
      {:ok, _lv, html} = live(conn, ~p"/users/forgot_password")

      assert html =~ "Reset Password"
      assert html =~ "We'll send a password reset link to your inbox"
    end

    test "sends password reset email for existing user", %{conn: conn} do
      user = user_fixture()
      {:ok, lv, _html} = live(conn, ~p"/users/forgot_password")

      form = form(lv, "#reset_password_form", user: %{email: user.email})
      render_submit(form)

      assert_email_sent(subject: "Reset your password - ProBASE Reconciliation")
    end

    test "shows success message even for non-existing email", %{conn: conn} do
      {:ok, lv, _html} = live(conn, ~p"/users/forgot_password")

      form = form(lv, "#reset_password_form", user: %{email: "<EMAIL>"})
      render_submit(form)

      # Should redirect with success message for security
      assert_redirected(lv, ~p"/")
    end
  end

  defp user_fixture(attrs \\ %{}) do
    {:ok, user} =
      attrs
      |> Enum.into(%{
        email: "test#{System.unique_integer()}@example.com",
        password: "hello world!"
      })
      |> Accounts.register_user()

    user
  end
end
