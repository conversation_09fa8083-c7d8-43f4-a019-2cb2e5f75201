defmodule Reconciliation.Services.ExcelizerExportTest do
  use ExUnit.Case, async: true
  alias Reconciliation.Services.ExcelizerExport

  describe "generate_transactions_excel_from_template/2" do
    test "returns error when template file does not exist" do
      # Create a mock transaction
      transaction = %{
        id: 1,
        transaction_date: ~D[2024-01-01],
        transaction_id: "TXN001",
        description: "Test transaction",
        reference: "REF001",
        amount: Decimal.new("100.00"),
        transaction_type: "Credit",
        account: "Test Account",
        category: "Test Category",
        currency: "USD",
        is_matched: false,
        match_confidence: nil,
        uploaded_file: %{
          filename: "test.csv",
          file_type: "file_a"
        },
        inserted_at: ~N[2024-01-01 10:00:00]
      }

      # Test with non-existent template
      result = ExcelizerExport.generate_transactions_excel_from_template([transaction], "test")

      assert {:error, error_message} = result
      assert String.contains?(error_message, "Template file not found")
    end

    test "handles empty transaction list gracefully" do
      # Test with empty transaction list
      result = ExcelizerExport.generate_transactions_excel_from_template([], "test")

      # Should still return error about missing template, but not crash
      assert {:error, _error_message} = result
    end

    test "handles transactions with nil values gracefully" do
      # Create a transaction with many nil values
      transaction = %{
        id: 1,
        transaction_date: nil,
        transaction_id: nil,
        description: nil,
        reference: nil,
        amount: nil,
        transaction_type: nil,
        account: nil,
        category: nil,
        currency: nil,
        is_matched: false,
        match_confidence: nil,
        uploaded_file: nil,
        inserted_at: nil
      }

      # Should not crash with nil values
      result = ExcelizerExport.generate_transactions_excel_from_template([transaction], "test")

      # Should return error about missing template, not about nil values
      assert {:error, error_message} = result
      assert String.contains?(error_message, "Template file not found")
    end
  end

  describe "generate_reconciliation_report_excel/3" do
    test "returns not implemented error" do
      run = %{
        file_a_name: "File A",
        file_b_name: "File B"
      }

      result = ExcelizerExport.generate_reconciliation_report_excel(run, [], [])

      # Should return not implemented error for now
      assert {:error, error_message} = result
      assert String.contains?(error_message, "not yet implemented")
    end
  end

  describe "helper functions behavior" do
    test "safe_string handles various input types" do
      # We can't test private functions directly, but we can test the behavior
      # through the public interface by checking that the function doesn't crash
      # with various data types

      transaction_with_various_types = %{
        id: 1,
        transaction_date: ~D[2024-01-01],
        transaction_id: 12345,  # number instead of string
        description: :atom_description,  # atom instead of string
        reference: ["list", "reference"],  # list instead of string
        amount: "not_a_number",  # string that's not a valid number
        transaction_type: %{type: "complex"},  # map instead of string
        account: nil,
        category: "",
        currency: "USD",
        is_matched: "yes",  # string instead of boolean
        match_confidence: "high",  # string instead of number
        uploaded_file: %{
          filename: 123,  # number instead of string
          file_type: :csv  # atom instead of string
        },
        inserted_at: ~N[2024-01-01 10:00:00]
      }

      # Should not crash even with mixed data types
      result = ExcelizerExport.generate_transactions_excel_from_template([transaction_with_various_types], "test")

      # Should return error about missing template, not about data type conversion
      assert {:error, error_message} = result
      assert String.contains?(error_message, "Template file not found")
    end
  end

  describe "fallback behavior" do
    test "handles missing template gracefully" do
      # This test verifies that the function handles missing templates correctly
      transaction = %{
        id: 1,
        transaction_date: ~D[2024-01-01],
        transaction_id: "TXN001",
        description: "Test transaction",
        reference: "REF001",
        amount: Decimal.new("100.00"),
        transaction_type: "Credit",
        account: "Test Account",
        category: "Test Category",
        currency: "USD",
        is_matched: true,
        match_confidence: Decimal.new("95.5"),
        uploaded_file: %{
          filename: "test.csv",
          file_type: "file_a"
        },
        inserted_at: ~N[2024-01-01 10:00:00]
      }

      # The function should handle the missing template gracefully
      result = ExcelizerExport.generate_transactions_excel_from_template([transaction], "test")

      # Should return a clear error message
      assert {:error, error_message} = result
      assert is_binary(error_message)
      assert String.length(error_message) > 0
    end
  end
end
